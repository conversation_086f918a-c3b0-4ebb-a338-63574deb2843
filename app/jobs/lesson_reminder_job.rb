class LessonReminderJob < ApplicationJob
  queue_as :default

  def perform(lesson)
    begin
      tenant = lesson.tenant
      course = lesson.lesson.course
      super_admin = tenant.super_admin
      lesson_title = lesson.lesson.title
      if lesson.class == Courses::LessonWebinar 
        student_url = lesson.url
        instructor_url = lesson.url
        lesson_type = "webinar"
      elsif lesson.class == Courses::LessonVideocall
        student_url = lesson.student_link
        instructor_url = lesson.instructor_link
        lesson_type = "video conferencia"
      end

      # UserMessagesManager.send_message_to_user(super_admin, :webinar_or_conference_reminder, template_variables: { lesson_name: lesson_title, course_name: course.name, event_url: instructor_url, school_name: tenant.school_name, user_firstname: super_admin.first_name, event_time: lesson.date })

      course.teachers.each do |instructor|
        UserMessagesManager.send_message_to_user(instructor, :webinar_or_conference_reminder, template_variables: { lesson_name: lesson_title, course_name: course.name, event_url: instructor_url, school_name: tenant.school_name, user_firstname: instructor.first_name, event_time: lesson.date, lesson_type: lesson_type })
      end

      course.subscriptions.each do |subscription|
        student = subscription.student
        next if student.nil?

        UserMessagesManager.send_message_to_user(student, :webinar_or_conference_reminder, template_variables: { lesson_name: lesson_title, course_name: course.name, event_url: student_url, school_name: tenant.school_name, user_firstname: student.first_name, event_time: lesson.date, lesson_type: lesson_type })
      end 

    rescue ArgumentError => e
      Rails.logger.error e
      Rollbar.error(e)
    end
  end
end
