class WebsiteFooterColumn < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website_footer
  
  validates :title, presence: true
  validates :position, presence: true, numericality: { greater_than: 0 }
  
  acts_as_list scope: :website_footer, column: :position
  
  # Ensure links is always an array
  def links
    super || []
  end
  
  # Helper method to add a link
  def add_link!(text, url, target = '_self')
    current_links = links.dup
    current_links << {
      'text' => text,
      'url' => url,
      'target' => target
    }
    update!(links: current_links)
  end
  
  # Helper method to update a link
  def update_link!(index, text, url, target = '_self')
    current_links = links.dup
    return false if index >= current_links.length
    
    current_links[index] = {
      'text' => text,
      'url' => url,
      'target' => target
    }
    update!(links: current_links)
  end
  
  # Helper method to remove a link
  def remove_link!(index)
    current_links = links.dup
    return false if index >= current_links.length
    
    current_links.delete_at(index)
    update!(links: current_links)
  end
end
