class Courses::<PERSON><PERSON><PERSON><PERSON><PERSON> < ApplicationRecord
  include Courses::<PERSON><PERSON><PERSON><PERSON><PERSON>

  has_many :questions, -> { order(position: :asc) }, class_name: "Courses::SurveyQuestion", foreign_key: :survey_id, dependent: :destroy
  has_many :survey_responses, class_name: "Subscriptions::SurveyResponse", foreign_key: :survey_id, dependent: :destroy
  has_many :survey_answers, through: :survey_responses, class_name: "Subscriptions::SurveyAnswer", dependent: :destroy

  belongs_to :lesson, foreign_key: :id, class_name: "Courses::Lesson"
  has_one :course, through: :lesson

  def subscription_survey_responses(subscription)
    survey_responses.where(subscription: subscription)
  end

  def duplicate(parent_id:, new_tenant: tenant)
    new_record = dup
    MultiTenantSupport.under_tenant new_tenant do
      new_record.id = parent_id
      new_record.tenant = new_tenant
      new_record.save!
    end
    questions.map { |q| q.duplicate(parent_id: new_record.id, new_tenant: new_tenant) }
  end
end
