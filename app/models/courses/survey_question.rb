class Courses::SurveyQuestion <  ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :survey, class_name: "Courses::<PERSON>on<PERSON>ur<PERSON>"
  belongs_to :related_lesson, class_name: "Courses::Lesson", required: false
  acts_as_list scope: :survey, sequential_updates: false, touch_on_update: false
  has_many :survey_answers, class_name: "Subscriptions::SurveyAnswer", dependent: :destroy, foreign_key: :question_id
  has_many :survey_questions, class_name: "Subscriptions::SurveyQuestion", dependent: :destroy, foreign_key: :question_id
  # has_one :statistic, class_name: "Courses::ExamQuestionStatistic", dependent: :destroy, foreign_key: :question_id

  def question_type=(val)
    self.type = "Courses::SurveyQuestion#{val.upcase_first}"
  end

  def question_type
    type.sub("Courses::SurveyQuestion", '').camelize
  end

  def duplicate(parent_id:, new_tenant: tenant)
    new_question = dup
    MultiTenantSupport.under_tenant new_tenant do
      new_question.survey_id = parent_id
      new_question.tenant = new_tenant
      pp parent_id
      pp "====================="
      new_question.save!
    end
  end
end
