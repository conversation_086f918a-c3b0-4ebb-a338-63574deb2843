class Websites::BodySectionPrice < ApplicationRecord
  include Websites::BodySectionContent

  has_many :price_cards, class_name: 'Websites::BodySectionPriceCard', dependent: :destroy
  accepts_nested_attributes_for :price_cards, allow_destroy: true

  # after_create :create_default_cards

  private

  def create_default_cards
    return if price_cards.any?

    cards_count.times do |index|
      card = price_cards.create!(
        title: "Plan #{index + 1}",
        subtitle: "Subtitle",
        description: "Everything you need to get started",
        position: index + 1,
      )
    end
  end
end
