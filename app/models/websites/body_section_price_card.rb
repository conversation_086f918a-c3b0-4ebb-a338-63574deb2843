class Websites::BodySectionPriceCard < ApplicationRecord
  include ImageUploader::Attachment.new(:image)

  belongs_to_tenant :tenant, optional: :true

  belongs_to :body_section_price, class_name: 'Websites::BodySectionPrice'
  has_many :price_card_items, class_name: 'Websites::BodySectionPriceCardItem', dependent: :destroy
  accepts_nested_attributes_for :price_card_items, allow_destroy: true

  acts_as_list scope: :body_section_price, sequential_updates: false, touch_on_update: false

  validates :title, presence: true
end
