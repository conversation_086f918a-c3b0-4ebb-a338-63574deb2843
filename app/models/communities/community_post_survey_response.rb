class Communities::CommunityPostSurveyResponse < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :community_post, class_name: 'Communities::CommunityPost'
  belongs_to :user, -> { unscope(where: :tenant_id) }, class_name: "User", optional: true

  validates :survey_answer_data, presence: true
  validates :user_id, uniqueness: { scope: :community_post_id, message: "has already responded to this survey" }

end
