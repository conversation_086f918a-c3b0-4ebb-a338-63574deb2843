class Website < ApplicationRecord
  belongs_to_tenant :tenant, optional: :true
  has_many :body_sections, -> { where(parent_banner_id: nil).order(position: :asc) }, class_name: "Websites::BodySection", dependent: :destroy
  has_one :footer, class_name: 'WebsiteFooter', dependent: :destroy

  validates :main_color, :header_color, :header_text_color, :footer_color, format: { with: /\A#(?:\h{3}){1,2}\z/, message: "must be an RGB color starting with #"}
  validates :title, presence: true

  enum state: { unpublished: 0, published: 1 }
  include ImageUploader::Attachment.new(:header_logo, store: :public_store)
  include ImageUploader::Attachment.new(:header_logo_sticky, store: :public_store)
  include ImageUploader::Attachment.new(:school_logo, store: :public_store)
  include ImageUploader::Attachment.new(:school_favicon, store: :public_store)

  scope :home_page, -> { find_by(title: 'Home') }
  # scope :homepage, -> { where(title: 'home', url: '/') }

  validate :restrict_website_limits, on: :create
  validate :unique_url_per_tenant_or_user

  after_commit :update_tenant_metric
  # after_create :ensure_homepage_footer

  def restrict_website_limits
    
    return unless tenant.present? && ['free', 'basic', 'pro'].include?(tenant.feature_plan_id)

    website_limits = tenant.feature_plan.website_limits
    websites = tenant.websites

    if website_limits < websites.count
      errors.add :base, "The total landing pages limit of #{website_limits} has been reached"
    end
  end

  def unique_url_per_tenant_or_user
    if tenant_id.present?
      if self.class.where(url: url, tenant_id: tenant_id).where.not(id: id).exists?
        errors.add(:url, "must be unique within the same tenant")
      end
    elsif user_id.present?
      if self.class.where(url: url, user_id: user_id).where.not(id: id).exists?
        errors.add(:url, "must be unique within the same user")
      end
    end
  end

  def duplicate_sections(template_page)

    page_tenant = self.tenant

    ActiveRecord::Base.transaction do
      last_position = body_sections.maximum(:position) || 0

      template_page.body_sections.each do |section|
        new_section = section.deep_dup
        new_section.website = self
        last_position += 1
        new_section.position = last_position
        MultiTenantSupport.under_tenant page_tenant do
          new_section.tenant = page_tenant
        end
        new_section.save!

        # Duplicate associated margins
        section.margins.each do |margin|
          new_margin = margin.deep_dup
          MultiTenantSupport.under_tenant page_tenant do
            new_margin.tenant = page_tenant
          end
          new_margin.body_section = new_section
          new_margin.save!
        end

        # Handle duplication of delegated content if necessary
        if section.content.present?
          new_content = section.content.deep_dup
          MultiTenantSupport.under_tenant page_tenant do
            new_content.tenant = page_tenant
          end
          new_content.template_section = true if new_content.class == Websites::BodySectionTestimonial
          new_content.body_section = new_section
          new_content.save!

          # Handle specific content types and their associations
          case new_content
          when Websites::BodySectionProductList
            courses = Courses::Course.where(tenant_id: page_tenant.id)
            courses.each do |course|
              new_course = Websites::BodySectionProductListsCourse.new(course: course, body_section_product_list: new_content)
              MultiTenantSupport.under_tenant page_tenant do
                new_course.tenant = page_tenant
              end
              new_course.save!
            end
          when Websites::BodySectionBanner, Websites::BodySectionTextMedia
            if section.content.website_button.present?
              new_button = section.content.website_button.deep_dup
              MultiTenantSupport.under_tenant page_tenant do
                new_button.tenant = page_tenant
              end
              new_button.save!
              new_content.update!(website_button: new_button)
              
              # Duplicate button styles
              section.content.website_button.button_styles.each do |button_style|
                new_button_style = button_style.deep_dup
                new_button_style.button = new_button
                MultiTenantSupport.under_tenant page_tenant do
                  new_button_style.tenant = page_tenant
                end
                new_button_style.save!

                # Duplicate styles associated with button styles
                new_style = if button_style.style&.style == 'custom'
                  button_style.style.deep_dup
                else
                  Websites::Style.where(tenant_id: page_tenant.id).find_or_initialize_by(style: button_style.style&.style)
                end

                MultiTenantSupport.under_tenant page_tenant do
                  new_style.tenant = page_tenant if new_style.new_record?
                end
                new_style.save!
                new_button_style.update!(style: new_style)
              end
            end
          when Websites::BodySectionTestimonial
            section.content.personal_testimonials.each do |testimonial|
              new_testimonial = testimonial.deep_dup
              new_testimonial.section_testimonial = new_content
              MultiTenantSupport.under_tenant page_tenant do
                new_testimonial.tenant = page_tenant
              end
              new_testimonial.save!
            end
          when Websites::BodySectionCounter
            section.content.counter_rows.each do |row|
              new_row = row.deep_dup
              new_row.body_section_counter = new_content
              new_row.save!
            end
          when Websites::BodySectionFaq
            section.content.frequent_questions.each do |question|
              new_question = question.deep_dup
              new_question.body_section_faq = new_content
              new_question.save!
            end
          when Websites::BodySectionCard
            section.content.card_columns.each do |column|
              new_column = column.deep_dup
              new_column.body_section_card = new_content
              new_column.save!
            end
          when Websites::BodySectionEmailLeadsForm

          end
        end
      end
    end
  rescue => e
    errors.add(:base, "Failed to duplicate sections: #{e.message}")
    return false
  end

  # Homepage identification
  # def homepage?
  #   title.downcase == 'home' && url == '/'
  # end

  # # Footer management methods
  # def ensure_footer!
  #   return footer if footer.present?
  #   return unless homepage?

  #   create_footer_with_defaults!
  # end

  # def create_footer_with_defaults!
  #   return unless homepage?

  #   # Migrate existing footer data if present
  #   footer_data = {
  #     copyright_label: footer_copyright_label || "",
  #     footer_color: footer_color || "#A270FE",
  #     social_links: social_links || []
  #   }

  #   create_footer!(footer_data)
  # end

  # # Legacy footer field accessors for backward compatibility
  # def footer_copyright_label
  #   return super unless homepage? && footer.present?
  #   footer.copyright_label
  # end

  # def footer_color
  #   return super unless homepage? && footer.present?
  #   footer.footer_color
  # end

  # def social_links
  #   return super unless homepage? && footer.present?
  #   footer.social_links
  # end

  private

  def update_tenant_metric
    return unless tenant.present?

    under_tenant_context(tenant) do
      tenant&.tenant_metric&.update(pages_created: tenant.pages_count_without_home)
    end
  end

  # def ensure_homepage_footer
  #   return unless homepage?

  #   # Create footer after website is created
  #   ensure_footer!
  # end
end
