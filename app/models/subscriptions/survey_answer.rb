class Subscriptions::SurveyAnswer < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :survey_response, class_name: "Subscriptions::SurveyResponse"
  belongs_to :question, class_name: "Courses::SurveyQuestion"
  belongs_to :user, class_name: "User", optional: true

  def survey_answer_type
    type.sub("Subscriptions::SurveyAnswer", '').camelize(:lower)
  end

  def score!(instructor_feedback:, instructor_score:, user:)
    self.user = user
    save!
    self
  end
end
