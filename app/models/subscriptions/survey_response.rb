class Subscriptions::SurveyResponse < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :subscription, class_name: "Subscriptions::Subscription", optional: true
  belongs_to :survey, class_name: "Courses::LessonSurvey"
  has_many :survey_questions, dependent: :destroy
  has_many :questions, through: :survey_questions, class_name: "Courses::SurveyQuestion"
  has_many :survey_answers, dependent: :destroy

  acts_as_list scope: [:subscription, :survey], column: :attempt_number, sequential_updates: false, touch_on_update: false

  after_commit :launch_finish_job, :create_survey_questions, on: :create
  after_destroy :destroy_progress

  scope :with_courses, ->(course_ids) { where(course: { id: course_ids }) if course_ids }
  scope :with_statuses, ->(statuses) { where(status: statuses) if statuses }
  scope :by_survey, ->(survey_id) { where(survey_id:) if survey_id }
  scope :by_student_name, ->(name) { where("concat(users.first_name,' ',users.last_name) like?", "%#{name}%") if name.present? }

  enum status: {
    initiated: "initiated",
    finished: "finished"
  }

  def launch_finish_job
    # SurveyAttemptJob.set(wait: survey.duration_minutes.minutes).perform_later(self) if initiated? && survey.duration_minutes
  end

  def total_questions
    questions.count
  end

  def automate_score_multiple_option_answers
    survey_answers.where(type: "Subscriptions::SurveyAnswerMultipleChoice").each do |answer|
      answer.score!
    end
  end

  def finish!
    self.status = "finished"
    self.finished_at = Time.zone.now
    automate_score_multiple_option_answers
  end

  def score!
  #   Courses::SurveyQuestionStatistic.compute_statistics(self.id, self.tenant_id)
  #   Subscriptions::Progress.create(tenant_id:, subscription_id:, lesson_id: self.survey_id, percentage: 1) if status == 'passed'
  end

  def destroy_progress
    progress = Subscriptions::Progress.find_by(subscription_id:, lesson_id: survey_id)
    progress&.destroy!
  end

  def create_survey_questions
    questions = survey.questions

    questions.each do |question|
      survey_questions.create!(question:)
    end
  end
end
