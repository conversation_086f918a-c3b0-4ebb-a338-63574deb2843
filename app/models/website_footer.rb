class WebsiteFooter < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website
  has_many :footer_columns, class_name: 'WebsiteFooterColumn', dependent: :destroy
  accepts_nested_attributes_for :footer_columns, allow_destroy: true

  include ImageUploader::Attachment.new(:bg_image, store: :public_store)

  enum template_type: {
    minimal_social: "minimal_social",       # Template 1
    compact_contact: "compact_contact",     # Template 2
    detailed_classic: "detailed_classic",   # Template 3
    extended_info: "extended_info"          # Template 4
  }, _prefix: true  

  enum social_links_style: {
    icons_in_bubbles: "icons_in_bubbles",
    icons_in_rectangle: "icons_in_rectangle",
    only_icons: "only_icons",
    only_text: "only_text"
  }, _prefix: true

  validates :website_id, uniqueness: true
  validate :website_must_be_homepage
  # validates :template_type, presence: true
  # validates :social_links_style, presence: true

  private

  def website_must_be_homepage
    return unless website

    unless website.homepage?
      errors.add(:website, "Footer can only be associated with homepage")
    end
  end
end
