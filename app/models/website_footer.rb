class WebsiteFooter < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website
  has_many :footer_columns, class_name: 'WebsiteFooterColumn', dependent: :destroy

  include ImageUploader::Attachment.new(:bg_image, store: :public_store)

  # Template name enum
  enum template_name: {
    template1: "template1",
    template2: "template2",
    template3: "template3",
    template4: "template4"
  }, _prefix: true

  validates :website_id, uniqueness: true
  validate :website_must_be_homepage
  validates :template_name, presence: true

  private

  def website_must_be_homepage
    return unless website

    unless website.homepage?
      errors.add(:website, "Footer can only be associated with homepage")
    end
  end
end
