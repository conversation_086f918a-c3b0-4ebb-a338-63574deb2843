class WebsiteFooter < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website
  
  validates :website_id, uniqueness: true
  validate :website_must_be_homepage
  
  # Footer-specific validations
  validates :footer_color, format: { with: /\A#(?:\h{3}){1,2}\z/, message: "must be an RGB color starting with #" }, allow_blank: true
  
  # Ensure social_links and contact_info are always arrays/hashes
  def social_links
    super || []
  end
  
  def contact_info
    super || {}
  end
  
  def additional_links
    super || {}
  end
  
  # Helper method to get social link by platform
  def social_link_for(platform)
    social_links.find { |link| link['platform'] == platform.to_s }
  end
  
  # Helper method to add or update social link
  def update_social_link!(platform, url)
    links = social_links.dup
    existing_link = links.find { |link| link['platform'] == platform.to_s }
    
    if existing_link
      existing_link['url'] = url
    else
      links << { 'platform' => platform.to_s, 'url' => url }
    end
    
    update!(social_links: links)
  end
  
  # Helper method to remove social link
  def remove_social_link!(platform)
    links = social_links.reject { |link| link['platform'] == platform.to_s }
    update!(social_links: links)
  end
  
  private
  
  def website_must_be_homepage
    return unless website
    
    unless website.homepage?
      errors.add(:website, "Footer can only be associated with homepage")
    end
  end
end
