class WebsiteFooter < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website
  has_many :footer_columns, class_name: 'WebsiteFooterColumn', dependent: :destroy

  include ImageUploader::Attachment.new(:bg_image, store: :public_store)

  validates :website_id, uniqueness: true
  validate :website_must_be_homepage

  # Color validations
  validates :bg_text_icon_colour, format: { with: /\A#(?:\h{3}){1,2}\z/, message: "must be an RGB color starting with #" }, allow_blank: true
  validates :bg_colour, format: { with: /\A#(?:\h{3}){1,2}\z/, message: "must be an RGB color starting with #" }, allow_blank: true

  # Template number validation
  validates :footer_template_number, presence: true, numericality: { greater_than: 0 }

  # Helper method to create a footer column
  def create_footer_column!(title, links = [], position = nil)
    footer_columns.create!(
      tenant: tenant,
      title: title,
      links: links,
      position: position || (footer_columns.maximum(:position) || 0) + 1
    )
  end

  private

  def website_must_be_homepage
    return unless website

    unless website.homepage?
      errors.add(:website, "Footer can only be associated with homepage")
    end
  end
end
