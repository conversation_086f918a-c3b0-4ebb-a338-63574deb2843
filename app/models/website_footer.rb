class WebsiteFooter < ApplicationRecord
  belongs_to_tenant :tenant
  belongs_to :website
has_many :footer_columns, class_name: 'WebsiteFooterColumn', dependent: :destroy

  include ImageUploader::Attachment.new(:bg_image, store: :public_store)

  validates :website_id, uniqueness: true
  validate :website_must_be_homepage

  # Template name validation
  validates :template_name, presence: true

  private

  def website_must_be_homepage
    return unless website

    unless website.homepage?
      errors.add(:website, "Footer can only be associated with homepage")
    end
  end
end
