class Extensions::AttachmentLoaderExtension < GraphQL::Schema::FieldExtension
    def resolve(object:, arguments:, context:)
      # IMPROVEMENT: make a model introspection and make sure the field is actually a has_one or has_many attached
      # binding.pry
      association = object.object.public_send(field.method_sym)
      case association
      when ActiveStorage::Attached::One
        record_type = association.record.class.to_s
        attachment_name = association.name
        context.dataloader.with(Sources::HasOneAttached, record_type, attachment_name).load(object.object.id)
        #BatchLoaders::HasOneAttachedLoader.for(record_type, attachment_name).load(object.object.id)
      when ActiveStorage::Attached::Many
        record_type = association.record.class.to_s
        attachment_name = association.name
        BatchLoaders::HasManyAttachedLoader.for(record_type, attachment_name).load(object.object)
      else
        yield(object, arguments, nil)
      end
    end
  end