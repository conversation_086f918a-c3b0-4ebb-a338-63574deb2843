class Extensions::AssociationLoaderExtension < GraphQL::Schema::FieldExtension
    def resolve(object:, arguments:, context:)
      # IMPROVEMENT: make a model introspection and make sure the field is actually a has_one or has_many attached
      # binding.pry
      record = object.object
      association_name = field.method_sym
      association = record.public_send(field.method_sym)
      case association
      when ActiveRecord::Associations::CollectionProxy
        model = record.class
        BatchLoaders::AssociationLoader.for(record.class, association_name).load(record)
      else
        yield(object, arguments)
      end
    end
  end
