module Types::Interfaces::Courses
  module SurveyQuestionType
    include Types::BaseInterface

    orphan_types(
      Types::Objects::Courses::SurveyQuestionTextType,
      Types::Objects::Courses::SurveyQuestionMultipleChoiceType
    )

    field :id, Integer, null: false
    field :survey_id, Integer, null: false
    field :question_type, Types::Enums::Surveys::QuestionsTypeType, null: false
    field :body, String, null: false
    field :position, Integer, null: false
    field :related_lesson, Types::Interfaces::Courses::LessonContentType, null: true
    field :related_lesson_id, Integer, null: true
    # field :statistic, Types::Objects::Courses::SurveyQuestionStatisticType, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false

    def self.resolve_type(object, _context)
      "Types::Objects::#{object.type}Type".constantize
    end
  end
end
