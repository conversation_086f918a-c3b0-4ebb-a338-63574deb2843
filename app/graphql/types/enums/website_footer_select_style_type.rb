module Types
  class Enums::WebsiteFooterSelectStyleType < Types::BaseEnum
    description "Footer select style options"

    value "ICONS_IN_BUBBLES", value: "icons_in_bubbles", description: "Icons displayed in bubble style"
    value "ICONS_IN_RECTANGLE", value: "icons_in_rectangle", description: "Icons displayed in rectangle style"
    value "ONLY_ICONS", value: "only_icons", description: "Display only icons without text"
    value "ONLY_TEXT", value: "only_text", description: "Display only text without icons"
  end
end
