# frozen_string_literal: true

module Types
  class Enums::Purchases::ProductTypeType < Types::BaseEnum
    description "Product type enum"

    value "COURSE", "A course", value: "Courses::Course"
    value "PACK", "A pack", value: "Courses::Pack"
    value "LICENSE", "Marketplace course license", value: "Marketplaces::License"
    value "COMMUNITY", "Community", value: "Communities::Community"
  end
end
