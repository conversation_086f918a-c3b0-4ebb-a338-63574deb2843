# frozen_string_literal: true

module Types
  class Enums::Purchases::ProductPurchaseStatusType < Types::BaseEnum
    description "Transaction status enum"

    value "INITIATED", "The transaction was successful", value: 'initiated'
    value "APPROVED", "The transaction was successful", value: 'approved'
    value "IN_PROGRESS", "The transaction is still in progress", value: 'in_progress'
    value "REJECTED", "The transaction was rejected by the provider", value: "rejected"
    value "FAILED", "A configuration error prevented the transaction from being completed", value: "failed"
    value "ERRORED", value: "errored"
    value "TIMED_OUT", "The transaction timed out", value: "timed_out"
    value "REFUNDED", "The transaction Refunded out", value: "refunded"
  end
end
