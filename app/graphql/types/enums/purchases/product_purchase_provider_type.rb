# frozen_string_literal: true

module Types
  class Enums::Purchases::ProductPurchaseProviderType < Types::BaseEnum
    description "Transaction status enum"

    value "MERCADO_PAGO", "Mercado Pago", value: 'mercado_pago'
    value "PAY_PAL", "PayPal", value: 'pay_pal'
    value "STRIPE", "Stripe", value: 'stripe'
    value "STRIPE_CONNECT", "Stripe Sabionet", value: 'stripe_connect'
    value "PAYU", "Payu", value: 'payu'
    value "LINK", "A través de un link personalizado", value: 'link'
    value "CUSTOM", "Legado, antiguo link", value: 'custom'
  end
end
