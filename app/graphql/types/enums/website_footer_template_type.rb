module Types
  class Enums::WebsiteFooterTemplateType < Types::BaseEnum
    description "Footer template options"

    value "MINIMAL_SOCIAL", value: "minimal_social", description: "Minimal layout with social icons"
    value "COMPACT_CONTACT", value: "compact_contact", description: "Compact layout with contact and social info"
    value "DETAILED_CLASSIC", value: "detailed_classic", description: "Detailed layout with contact and navigation"
    value "EXTENDED_INFO", value: "extended_info", description: "Extended layout with address, links, and social"
  end
end
