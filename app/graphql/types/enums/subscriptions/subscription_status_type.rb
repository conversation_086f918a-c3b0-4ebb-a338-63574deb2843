# frozen_string_literal: true

module Types
    class Enums::Subscriptions::SubscriptionStatusType < Types::BaseEnum
      description "The periodic subscription status"

      value "ACTIVE", "The subscription is active", value: 'active'
      value "PAUSED", "The student has paused the subscription", value: 'paused'
      value "CANCELLED", "The student has cancelled the subscription", value: 'cancelled'
      value "EXPIRED", value: 'expired'
    end
  end
  