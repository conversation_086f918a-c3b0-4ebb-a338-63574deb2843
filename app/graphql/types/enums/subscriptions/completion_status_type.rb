# frozen_string_literal: true

module Types
  class Enums::Subscriptions::CompletionStatusType < Types::BaseEnum
    description "The completion status of this subscription"

    value "INVALID", "The associated course has no lessons, so nothing exists that can be completed"
    value "NOT_STARTED", "The student has not started the associated course"
    value "IN_PROGRESS", "The student has started, but not finished, the associated course"
    value "FINISHED", "The student has finished the associated course"
  end
end
