module Types
  module Inputs
    module Affiliates
      class AffiliateInformationInputType < ::Types::BaseInputObject
        description "Input type for affiliate information"

        argument :id, ID, required: false
        argument :nature, String, required: false
        argument :location, String, required: false
        argument :birthdate, GraphQL::Types::ISO8601Date, required: false
        argument :identity_document, String, required: false
        argument :facebook, String, required: false
        argument :instagram, String, required: false
        argument :whatsapp, String, required: false
        argument :email, String, required: false
        argument :linked_in, String, required: false
        argument :youtube, String, required: false
        argument :reddit, String, required: false
      end
    end
  end
end
