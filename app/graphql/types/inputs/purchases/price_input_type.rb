# frozen_string_literal: true

module Types
  class Inputs::Purchases::PriceInputType < Types::BaseInputObject
    argument :product_type, Types::Enums::Purchases::ProductTypeType, required: false
    argument :product_id, Integer, required: false

    argument :price, Float, required: false
    argument :currency, String, required: false
    argument :has_discounted_price, <PERSON><PERSON><PERSON>, required: false
    argument :discounted_price, Float, required: false
    argument :quantity, Integer, required: false
    argument :subscription, Boolean, required: false
    argument :trial_period_days, Integer, required: false
    argument :mercado_pago_installments, Integer, required: false
    argument :installments_enabled, Bo<PERSON>an, required: false
    argument :thank_you_page_url, String, required: false
    argument :thank_you_page_enabled, <PERSON><PERSON><PERSON>, required: false
    argument :payment_method_ids, [Integer], required: false
    argument :payment_link, String, required: false
  end
end
