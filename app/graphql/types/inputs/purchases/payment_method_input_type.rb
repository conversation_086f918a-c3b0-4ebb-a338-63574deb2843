# frozen_string_literal: true

module Types
  class Inputs::Purchases::PaymentMethodInputType < Types::BaseInputObject
    argument :name, String, required: false
    argument :payment_method_type, Types::Enums::Purchases::ProductPurchaseProviderType, as: :type, required: false, prepare: ->(pmt, _ctx) {
      pp pmt
      "Purchases::PaymentMethod#{pmt.camelize}"
    }
    argument :config, GraphQL::Types::JSON, required: false
  end
end
