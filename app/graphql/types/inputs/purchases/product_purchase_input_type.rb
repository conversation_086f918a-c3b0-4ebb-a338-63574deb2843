# frozen_string_literal: true

module Types
  class Inputs::Purchases::ProductPurchaseInputType < Types::BaseInputObject
    argument :payment_method_id, Integer, required: false
    argument :price_id, Integer, required: false
    argument :quantity, Integer, required: false
    argument :visibility, Bo<PERSON>an, required: false
    argument :token_subscription, String, required: false
    argument :coupon_code, String, required: false
    argument :provider_data, GraphQL::Types::JSON, required: false
  end
end
