module Types
  class Inputs::Websites::BodySectionPriceCardInputType < Types::BaseInputObject
    argument :id, ID, required: false
    
    argument :background_color, String, required: false
    argument :text_color, String, required: false
    argument :image, GraphQL::Types::JSON, required: false
    argument :font, String, required: false

    argument :title, String, required: false
    argument :title_size, String, required: false
    argument :title_font_alignment, String, required: false
    argument :title_font_bold, Bo<PERSON>an, required: false
    argument :title_font_cursive, <PERSON><PERSON>an, required: false
    argument :title_font_underlined, <PERSON><PERSON>an, required: false
    argument :title_font_line_through, <PERSON><PERSON>an, required: false

    argument :subtitle, String, required: false
    argument :subtitle_size, String, required: false
    argument :subtitle_font_alignment, String, required: false
    argument :subtitle_font_bold, <PERSON><PERSON>an, required: false
    argument :subtitle_font_cursive, <PERSON><PERSON>an, required: false
    argument :subtitle_font_underlined, Boolean, required: false
    argument :subtitle_font_line_through, <PERSON><PERSON><PERSON>, required: false

    argument :description, String, required: false
    argument :description_size, String, required: false
    argument :description_font_alignment, String, required: false
    argument :description_font_bold, Bo<PERSON>an, required: false
    argument :description_font_cursive, <PERSON><PERSON><PERSON>, required: false
    argument :description_font_underlined, Boolean, required: false
    argument :description_font_line_through, Boolean, required: false

    argument :icon_color, String, required: false
    argument :visibility, Boolean, required: false

    # Button
    argument :button_type, String, required: false
    argument :button_text, String, required: false
    argument :button_url, String, required: false
    argument :button_visibility, Boolean, required: false

    argument :position, Integer, required: false
    argument :price_card_items_attributes, [Inputs::Websites::BodySectionPriceCardItemInputType], required: false
    argument :_destroy, Boolean, required: false
  end
end
