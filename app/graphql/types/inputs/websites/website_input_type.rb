module Types::Inputs::Websites
  class WebsiteInputType < Types::BaseInputObject
    argument :title, String, required: false
    argument :url, String, required: false
    argument :state, Integer, required: false
    argument :main_color, String, required: false
    argument :header_code, String, required: false
    argument :header_text_font, String, required: false
    argument :header_logo_size, Integer, required: false
    argument :header_color, String, required: false
    argument :header_text_color, String, required: false
    argument :header_transparent, <PERSON><PERSON>an, required: false
    argument :show_link_course, <PERSON><PERSON>an, required: false
    argument :show_link_pack, <PERSON><PERSON>an, required: false
    argument :social_links, GraphQL::Types::JSON, required: false

    argument :header_logo, GraphQL::Types::JSON, required: false
    argument :header_logo_sticky, GraphQL::Types::JSON, required: false

    argument :header_links, GraphQL::Types::JSON, required: false
  end
end
