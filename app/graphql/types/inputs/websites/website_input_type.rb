module Types::Inputs::Websites
  class WebsiteInputType < Types::BaseInputObject
    argument :title, String, required: false
    argument :url, String, required: false
    argument :state, Integer, required: false
    argument :main_color, String, required: false
    argument :header_code, String, required: false
    argument :header_text_font, String, required: false
    argument :header_logo_size, Integer, required: false
    argument :header_color, String, required: false
    argument :header_text_color, String, required: false
    argument :header_transparent, <PERSON><PERSON><PERSON>, required: false
    argument :footer_copyright_label, String, required: false
    argument :footer_color, String, required: false
    argument :show_link_course, <PERSON>olean, required: false
    argument :show_link_pack, <PERSON><PERSON>an, required: false
    argument :social_links, GraphQL::Types::JSON, required: false

    argument :header_logo, GraphQL::Types::JSON, required: false
    argument :header_logo_sticky, GraphQL::Types::JSON, required: false

    argument :header_links, GraphQL::Types::JSON, required: false

    # Footer configuration (only for homepage websites)
    argument :footer, Types::Inputs::WebsiteFooterInputType, required: false
  end
end
