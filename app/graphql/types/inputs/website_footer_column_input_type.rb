module Types
  class Inputs::WebsiteFooterColumnInputType < Types::BaseInputObject
    description "Input type for website footer column"

    argument :title, String, required: false, description: "Column title"
    argument :links, [GraphQL::Types::JSON], required: false, description: "Array of links in this column"
    argument :position, Integer, required: false, description: "Column position"
  end
end
