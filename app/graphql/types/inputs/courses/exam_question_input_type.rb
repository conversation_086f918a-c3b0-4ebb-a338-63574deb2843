module Types
  class Inputs::Courses::ExamQuestionInputType < Types::BaseInputObject
    description "Input type for question"

    argument :exam_id, Integer, required: false
    argument :question_type, Types::Enums::Exams::QuestionTypeType, required: false, as: :type, prepare: ->(val, _ctx) { "Courses::ExamQuestion#{val}" unless val.nil? }
    argument :weight, Integer, required: false
    argument :body, String, required: false
    argument :position, Integer, required: false
    argument :feedback, String, required: false
    argument :picture, GraphQL::Types::JSON, required: false
    argument :answer_options, [Types::Inputs::Courses::ExamAnswerOptionInputType], required: false
    argument :related_lesson_id, Integer, required: false
  end
end