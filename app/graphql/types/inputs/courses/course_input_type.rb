class Types::Inputs::Courses::CourseInputType < Types::BaseInputObject
  description "Input type for courses"

  argument :name, String, required: false
  argument :category_id, Integer, required: false
  argument :description, String, required: false
  argument :start_date, GraphQL::Types::ISO8601Date, required: false
  argument :end_date, GraphQL::Types::ISO8601Date, required: false
  argument :has_expiration, Bo<PERSON>an, required: false
  argument :expires_in_days, Integer, required: false

  argument :is_free, <PERSON><PERSON><PERSON>, required: false

  argument :allow_comments, <PERSON><PERSON><PERSON>, required: false
  argument :featured, <PERSON><PERSON><PERSON>, required: false
  argument :has_certificate, Bo<PERSON>an, required: false
  argument :certificate_qr_enabled, <PERSON><PERSON>an, required: false
  argument :certificate_qr_position, String, required: false
  argument :certificate_color, String, required: false
  argument :certificate_font, String, required: false
  argument :certificate_date_alignment, String, required: false
  argument :certificate_custom_text_alignment, String, required: false
  argument :certificate_custom_text, String, required: false
  argument :certificate_type, String, required: false
  argument :reviews, Boolean, required: false
  argument :is_visible, <PERSON><PERSON><PERSON>, required: false
  argument :show_languaje, <PERSON><PERSON><PERSON>, required: false 
  argument :show_student_counter, <PERSON><PERSON><PERSON>, required: false
  argument :show_course_duration, <PERSON><PERSON>an, required: false
  argument :show_date_updated, Boolean, required: false
  argument :duration_course, String, required: false
  argument :certificate_picture, GraphQL::Types::JSON, "The background certificate picture, as an Uppy field", required: false

  argument :promo_picture, GraphQL::Types::JSON, "The promotional picture, as an Uppy field", required: false
  argument :promo_video, GraphQL::Types::JSON, "The promotional video, as an Uppy field", required: false
  argument :cover_picture, GraphQL::Types::JSON, "The cover picture, as an Uppy field", required: false
  argument :video_preview_picture, GraphQL::Types::JSON, "The cover picture, as an Uppy field", required: false

  #SEO
  argument :seo_title, String, "SEO title", required: false
  argument :seo_description, String, "SEO description", required: false

  argument :custom_url_name, String, "Custom URL", required: false
  argument :is_available_on_marketplace, Boolean, 'Whether this course should be available on Marketplace', required: false
  argument :is_licensed, Boolean, 'Whether this course is purchased via license from Marketplace', required: false
  argument :marketplace_categories_ids, [Integer], required: false
  argument :teacher_ids, [Integer], required: false

  argument :is_available_on_affiliate, Boolean, 'Whether this course should be available on Affiliate Market or not', required: false
  argument :affiliate_setting, Types::Inputs::Courses::CourseAffiliateSettingInputType, required: false
end