module Types
  class Inputs::Courses::LessonInputType < Types::BaseInputObject
    argument :title, String, "The title of the lesson", required: false
    argument :position, Integer, "The position of the lesson", required: false
    
    argument :description, String, required: false
    argument :allows_comments, <PERSON><PERSON><PERSON>, required: false
    argument :is_public, <PERSON><PERSON><PERSON>, required: false
    argument :my_module_id, Integer, "The module this lesson belongs to", required: false
  end
end
