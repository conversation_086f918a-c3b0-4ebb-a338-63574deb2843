class Types::Inputs::Courses::CourseAffiliateSettingInputType < Types::BaseInputObject
  description "Input type for courses affiliate settings"

  argument :commission_percentage, Integer, required: false
  argument :landing_page_url, String, required: false
  argument :affiliate_invitation_link, String, required: false
  argument :attribution_model, String, required: false
  argument :cookies_duration_time, Integer, required: false
  argument :approval_requirement, <PERSON><PERSON><PERSON>, required: false
  argument :description, String, required: false
end
