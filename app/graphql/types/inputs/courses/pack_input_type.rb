class Types::Inputs::Courses::PackInputType < Types::BaseInputObject
  description "Input type for courses"

  argument :name, String, required: false
  argument :description, String, required: false
  argument :is_visible, Boolean, required: false
  argument :picture, GraphQL::Types::JSON, "The promotional picture, as a direct upload", required: false
  argument :start_date, GraphQL::Types::ISO8601Date, required: false
  argument :end_date, GraphQL::Types::ISO8601Date, required: false

  argument :is_free, Boolean, required: false

  argument :course_ids, [Integer], "The IDs of the courses belonging to this pack", required: false
  
  argument :promo_picture, GraphQL::Types::JSON, "The promotional picture, as an Uppy field", required: false
  argument :promo_video, GraphQL::Types::JSON, "The promotional video, as an Uppy field", required: false
  argument :cover_picture, GraphQL::Types::JSON, "The cover picture, as an Uppy field", required: false
  argument :video_preview_picture, GraphQL::Types::JSON, "The cover picture, as an Uppy field", required: false

  #SEO
  argument :seo_title, String, "SEO title", required: false
  argument :seo_description, String, "SEO description", required: false
end