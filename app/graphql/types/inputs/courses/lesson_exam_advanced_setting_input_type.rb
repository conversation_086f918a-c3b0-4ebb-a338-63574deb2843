class Types::Inputs::Courses::LessonExamAdvancedSettingInputType < Types::BaseInputObject
  description "Input type for advanced settings of an exam"

  argument :allow_exam_retake, <PERSON><PERSON><PERSON>, "Is retake of the exam allowed?", required: false, default_value: false
  argument :number_of_questions, Integer, "How many questions to be added in an exam?", required: false, default_value: 0
  argument :show_all_questions, <PERSON><PERSON><PERSON>, "Should we show all the question or a definite number?", required: false, default_value: true
  argument :show_correct_answers, <PERSON><PERSON><PERSON>, "Should we show correct answer after exam completion?", required: false, default_value: true
  argument :show_random_questions, <PERSON><PERSON><PERSON>, "Should we generate a random set of quetions from question bank?", required: false, default_value: false
end