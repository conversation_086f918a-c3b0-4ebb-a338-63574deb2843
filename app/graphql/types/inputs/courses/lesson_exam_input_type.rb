module Types
  class Inputs::Courses::LessonExamInputType < Types::Inputs::Courses::LessonInputType
    argument :duration_minutes, Integer, required: false
    argument :passing_percentage, Float, "A percentage as a ratio, between 0 and 1", required: false
    argument :max_retries, Integer, required: false
    argument :advanced_settings, Types::Inputs::Courses::LessonExamAdvancedSettingInputType, required: false, default_value: { allow_exam_retake: false, number_of_questions: 0, show_all_questions: true, show_correct_answers: true, show_random_questions: false }
  end
end
