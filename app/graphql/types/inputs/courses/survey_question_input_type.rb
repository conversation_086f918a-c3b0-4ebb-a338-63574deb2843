module Types
  class Inputs::Courses::SurveyQuestionInputType < Types::BaseInputObject
    description "Input type for question"

    argument :survey_id, Integer, required: false
    argument :question_type, Types::Enums::Surveys::QuestionsTypeType, required: false, as: :type, prepare: ->(val, _ctx) { "Courses::SurveyQuestion#{val}" unless val.nil? }
    argument :body, String, required: false
    argument :position, Integer, required: false
    argument :answer_options, [Types::Inputs::Courses::SurveyAnswerOptionInputType], required: false
    argument :related_lesson_id, Integer, required: false
  end
end
