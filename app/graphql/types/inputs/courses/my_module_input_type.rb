class Types::Inputs::Courses::MyModuleInputType < Types::BaseInputObject
  description "Input type for modules"

  # argument :id, Integer, required: false
  argument :title, String, "Course title", required: false
  argument :position, Integer, required: false
  argument :required_to_next, <PERSON><PERSON><PERSON>, required: false
  argument :course_id, Integer, required: false
  argument :release_date, GraphQL::Types::ISO8601DateTime, required: false
end