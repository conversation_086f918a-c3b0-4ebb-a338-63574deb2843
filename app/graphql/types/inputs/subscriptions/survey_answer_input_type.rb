class Types::Inputs::Subscriptions::SurveyAnswerInputType < Types::BaseInputObject
  description "Input type for survey answer"

  argument :survey_response_id, Integer, required: true
  argument :question_id, Integer, required: true
  argument :answer, GraphQL::Types::JSON, required: true
  argument :survey_answer_type, Types::Enums::Surveys::QuestionsTypeType, required: true, as: :type, prepare: ->(val, _ctx) { "Subscriptions::SurveyAnswer#{val}" }
  argument :time_taken, Integer, default_value: 0, description: 'Time taken to answer the question in seconds.'
end
