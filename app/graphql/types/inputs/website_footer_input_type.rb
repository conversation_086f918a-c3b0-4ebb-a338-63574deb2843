module Types
  class Inputs::WebsiteFooterInputType < Types::BaseInputObject
    description "Input type for website footer"

    argument :copyright_label, String, required: false, description: "Footer copyright text"
    argument :footer_color, String, required: false, description: "Footer background color (hex format)"
    argument :description, String, required: false, description: "Footer description text"
    argument :social_links, [GraphQL::Types::JSON], required: false, description: "Array of social media links"
    argument :contact_info, GraphQL::Types::JSON, required: false, description: "Contact information object"
    argument :additional_links, GraphQL::Types::JSON, required: false, description: "Additional footer links"
    argument :layout_style, String, required: false, description: "Footer layout style (default, minimal, extended)"
    argument :show_social_links, <PERSON><PERSON>an, required: false, description: "Whether to show social links"
    argument :show_contact_info, Boolean, required: false, description: "Whether to show contact information"
    argument :show_additional_links, Boolean, required: false, description: "Whether to show additional links"
    argument :newsletter_enabled, Boolean, required: false, description: "Whether newsletter signup is enabled"
    argument :newsletter_title, String, required: false, description: "Newsletter section title"
    argument :newsletter_description, String, required: false, description: "Newsletter section description"
    argument :newsletter_button_text, String, required: false, description: "Newsletter button text"
    argument :show_powered_by, Boolean, required: false, description: "Whether to show powered by text"
    argument :custom_footer_text, String, required: false, description: "Custom footer text"
  end
end
