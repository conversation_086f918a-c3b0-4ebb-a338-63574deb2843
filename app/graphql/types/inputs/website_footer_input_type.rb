module Types
  class Inputs::WebsiteFooterInputType < Types::BaseInputObject
    description "Input type for website footer"

    # Footer template and styling
    argument :template_name, Types::Enums::WebsiteFooterTemplateType, required: false, description: "Footer template name"
    argument :select_style, Types::Enums::WebsiteFooterSelectStyleType, required: false, description: "Footer select style"
    argument :bg_text_icon_colour, String, required: false, description: "Background text and icon color (hex format)"
    argument :bg_colour, String, required: false
    argument :bg_colour_visibility, <PERSON>olean, required: false
    argument :bg_image, GraphQL::Types::JSON, required: false
    argument :bg_image_visibility, Boolean, required: false

    # Contact information
    argument :contact_information_visibility, <PERSON><PERSON>an, required: false
    argument :contact_information_title, String, required: false
    argument :contact_information_address, String, required: false

    # Social networks
    argument :social_networks_visibility, Boolean, required: false
    # Footer columns
    argument :footer_columns, [Types::Inputs::WebsiteFooterColumnInputType], required: false
    argument :columns_visibility, Boolean, required: false
  end
end
