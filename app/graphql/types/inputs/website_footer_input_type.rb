module Types
  class Inputs::WebsiteFooterInputType < Types::BaseInputObject
    description "Input type for website footer"

    # Footer template and styling
    argument :footer_template_number, Integer, required: false, description: "Footer template number"
    argument :bg_text_icon_colour, String, required: false, description: "Background text and icon color (hex format)"
    argument :bg_colour, String, required: false, description: "Background color (hex format)"
    argument :bg_colour_visibility, <PERSON><PERSON>an, required: false, description: "Whether background color is visible"
    argument :bg_image, GraphQL::Types::JSON, required: false, description: "Background image data"
    argument :bg_image_visibility, Bo<PERSON>an, required: false, description: "Whether background image is visible"

    # Contact information
    argument :contact_information_visibility, Bo<PERSON>an, required: false, description: "Whether contact information is visible"
    argument :contact_information_title, String, required: false, description: "Contact information section title"
    argument :contact_information_address, String, required: false, description: "Contact information address"

    # Social networks
    argument :social_networks_visibility, Boolean, required: false, description: "Whether social networks are visible"

    # Footer columns visibility
    argument :columns_visibility, <PERSON><PERSON>an, required: false, description: "Whether footer columns are visible"
  end
end
