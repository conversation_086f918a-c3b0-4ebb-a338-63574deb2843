class Types::Inputs::Communities::CommunityInputType < Types::BaseInputObject
  description "Input type for community"

  argument :name, String, required: false
  argument :description, String, required: false
  argument :start_date, GraphQL::Types::ISO8601Date, required: false
  argument :end_date, GraphQL::Types::ISO8601Date, required: false
  argument :has_expiration, <PERSON><PERSON>an, required: false
  argument :expires_in_days, Integer, required: false

  argument :is_free, <PERSON><PERSON><PERSON>, required: false

  argument :allow_comments, <PERSON><PERSON><PERSON>, required: false
  argument :featured, <PERSON><PERSON><PERSON>, required: false
  argument :is_visible, <PERSON><PERSON><PERSON>, required: false
  argument :show_preview, <PERSON><PERSON><PERSON>, required: false 

  argument :promo_picture, GraphQL::Types::JSON, "The promotional picture, as an Uppy field", required: false
  argument :cover_picture, GraphQL::Types::JSON, "The cover picture, as an Uppy field", required: false
  argument :teacher_ids, [Integer], required: false

  # Polymorphic association arguments
  argument :associated_id, Integer, "ID of the associated entity (Academy, Course, Pack, Group)", required: false
  argument :associated_type, String, "Type of the associated entity (Academy, Course, Pack, Group)", required: false
end
