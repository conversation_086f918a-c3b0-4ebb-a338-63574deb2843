class Types::Inputs::Communities::CommunityPostInputType < Types::BaseInputObject
  description "Input type for community"

  argument :title, String, required: false
  argument :content, String, required: false
  argument :attachments, Types::Inputs::Communities::CommunityPostAttachmentInputType, required: false
  argument :attachment, GraphQL::Types::JSON, required: false
  argument :media_link, String, required: false

  argument :survey_options, [Types::Inputs::Communities::CommunityPostSurveyOptionInputType], required: false
end
