module Types
  class MutationType < Types::BaseObject
    field :coupon_create, mutation: Mutations::Purchases::CouponCreate
    field :coupon_update, mutation: Mutations::Purchases::CouponUpdate
    field :coupon_delete, mutation: Mutations::Purchases::CouponDelete
    field :product_purchase_approve, mutation: Mutations::Purchases::ProductPurchaseApprove
    field :payment_method_delete, mutation: Mutations::Purchases::PaymentMethodDelete
    field :payment_method_update, mutation: Mutations::Purchases::PaymentMethodUpdate
    field :payment_method_create, mutation: Mutations::Purchases::PaymentMethodCreate
    field :price_delete, mutation: Mutations::Purchases::PriceDelete
    field :price_update, mutation: Mutations::Purchases::PriceUpdate
    field :price_create, mutation: Mutations::Purchases::PriceCreate
    field :lesson_content_delete, mutation: Mutations::Media::LessonContentDelete
    field :lesson_content_create, mutation: Mutations::Media::LessonContentCreate
    field :get_stripe_dashboard_url, mutation: Mutations::GetStripeDashboardUrl
    field :delete_stripe_card, mutation: Mutations::DeleteStripeCard
    field :get_stripe_receipt_url, mutation: Mutations::GetStripeReceiptUrl
    field :buy_plan_subscription, mutation: Mutations::BuyPlanSubscription
    field :audit_version_update, mutation: Mutations::AuditVersionUpdate
    field :create_checkout_session, mutation: Mutations::CreateCheckoutSession
    field :create_notice, mutation: Mutations::CreateNotice
    field :delete_notice, mutation: Mutations::DeleteNotice

    field :custom_sign_up_form_edit, mutation: Mutations::CustomSignUpFormEdit
    field :read_notifications, mutation: Mutations::CurrentUser::ReadNotifications
    field :users_bulk_upload, mutation: Mutations::Users::UsersBulkUpload
    field :product_purchase_verify, mutation: Mutations::Purchases::ProductPurchaseVerify
    field :product_purchase_initiate, mutation: Mutations::Purchases::ProductPurchaseInitiate
    field :product_purchase_update, mutation: Mutations::Purchases::ProductPurchaseUpdate

    field :subscribe_to_free_course, mutation: Mutations::Purchases::SubscribeToFreeCourse
    field :subscribe_to_free_pack, mutation: Mutations::Purchases::SubscribeToFreePack
    field :subscribe_to_free_community, mutation: Mutations::Purchases::SubscribeToFreeCommunity

    field :set_free_plan, mutation: Mutations::Accounts::SetFreePlan
    field :reaction_delete, mutation: Mutations::Social::ReactionDelete
    field :reaction_create, mutation: Mutations::Social::ReactionCreate
    field :comment_delete, mutation: Mutations::Social::CommentDelete
    field :comment_update, mutation: Mutations::Social::CommentUpdate
    field :comment_create, mutation: Mutations::Social::CommentCreate

    ###########################################
    # Marketplace - mutations - License       #
    ###########################################
    # field :create_license, mutation: Mutations::Marketplace::CreateLicense
    field :subscribe_to_licensed_course, mutation: Mutations::Marketplace::SubscribeToLicensedCourse

    ###########################################
    # User - mutations                   #
    ###########################################
    implements Mutations::UserMutations

    ###########################################
    # Current User - mutations                 #
    ###########################################
    implements Mutations::CurrentUserMutations

    ###########################################
    # My Module - mutations                   #
    ###########################################
    implements Mutations::MyModuleMutations

    ############################################
    # Course - mutations                       #
    ###########################################
    implements Mutations::CourseMutations

    ############################################
    # Community - mutations                       #
    ###########################################
    implements Mutations::CommunityMutations

    ############################################
    # ExamAttempt - mutations                  #
    ###########################################
    implements Mutations::ExamAttemptMutations

    ############################################
    # ExamAnswer - mutations                  #
    ###########################################
    implements Mutations::ExamAnswerMutations

    # Subscription - mutations                 #
    ###########################################
    implements Mutations::SubscriptionMutations
    
    # Refund - mutations                 #
    ###########################################
    implements Mutations::RefundMutations

    # Stripe Connect - mutations                 #
    ###########################################
    implements Mutations::StripeConnectMutations

    ###########################################
    # Lesson - mutations -exam                #
    ###########################################
    implements Mutations::Courses::ExamMutations

    ###########################################
    # Lesson - mutations -survey                #
    ###########################################
    implements Mutations::Courses::SurveyMutations

    # Setting - mutations -exam                #
    ###########################################
    implements Mutations::GlobalSettingMutations

    ###########################################
    # Group - mutations -exam                #
    ###########################################
    implements Mutations::GroupMutations

    ###########################################################
    # Domains
    ##########################################################
    implements Mutations::EmailMutations
    
    # Triggers
    ##########################################################
    implements Mutations::TriggerMutations

    # Student Reports
    ##########################################################
    implements Mutations::ReportMutations


    # Affiliates
    ##########################################################
    implements Mutations::AffiliateMutations

    # Payment Mutations
    ##########################################################
    implements Mutations::PaymentMutations

    ############################################
    # Lesson - mutations                       #
    ###########################################
    field :delete_lesson, mutation: Mutations::Courses::DeleteLesson
    field :reorder_lessons, mutation: Mutations::Courses::Lessons::ReorderLessons

    ###########################################
    # Lesson - mutations - video                #
    ###########################################
    field :create_lesson_video, mutation: Mutations::Courses::CreateLessonVideo
    field :update_lesson_video, mutation: Mutations::Courses::UpdateLessonVideo

    ###########################################
    # Lesson - mutations - audio                #
    ###########################################
    field :create_lesson_audio, mutation: Mutations::Courses::CreateLessonAudio
    field :update_lesson_audio, mutation: Mutations::Courses::UpdateLessonAudio

    ###########################################
    # Lesson - mutations - scorm                #
    ###########################################
    field :create_lesson_scorm, mutation: Mutations::Courses::CreateLessonScorm
    field :update_lesson_scorm, mutation: Mutations::Courses::UpdateLessonScorm

    ###########################################
    # Lesson - mutations - pdf                #
    ###########################################
    field :create_lesson_pdf, mutation: Mutations::Courses::CreateLessonPdf
    field :update_lesson_pdf, mutation: Mutations::Courses::UpdateLessonPdf

    ###########################################
    # Lesson - mutations - downloadable        #
    ###########################################
    field :create_lesson_downloadable, mutation: Mutations::Courses::CreateLessonDownloadable
    field :update_lesson_downloadable, mutation: Mutations::Courses::UpdateLessonDownloadable

    ###########################################
    # Lesson - mutations - videocall          #
    ###########################################
    field :create_lesson_videocall, mutation: Mutations::Courses::CreateLessonVideocall
    field :update_lesson_videocall, mutation: Mutations::Courses::UpdateLessonVideocall

    ###########################################
    # Lesson - mutations - webinar            #
    ###########################################
    field :create_lesson_webinar, mutation: Mutations::Courses::CreateLessonWebinar
    field :update_lesson_webinar, mutation: Mutations::Courses::UpdateLessonWebinar

    ###########################################
    # Lesson - mutations - html               #
    ###########################################
    field :create_lesson_html, mutation: Mutations::Courses::CreateLessonHtml
    field :update_lesson_html, mutation: Mutations::Courses::UpdateLessonHtml

    ###########################################
    # Category - mutations                   #
    ###########################################

    implements Mutations::CategoryMutations

    # TAGS
    implements Mutations::TagMutations

    ###########################################################
    # Website
    ##########################################################
    field :update_page, mutation: Mutations::Websites::UpdateWebsite

    field :create_page, mutation: Mutations::Websites::CreateWebsite
    field :delete_website_footer_column, mutation: Mutations::Websites::DeleteWebsiteFooterColumn
    field :delete_page, mutation: Mutations::Websites::DeleteWebsite
    field :apply_template, mutation: Mutations::Websites::ApplyTemplate

    ###########################################################
    # Website BodySection          #
    ##########################################################
    field :reorder_sections, mutation: Mutations::Websites::ReorderSections
    field :delete_body_section, mutation: Mutations::Websites::DeleteBodySection
    field :update_body_section, mutation: Mutations::Websites::UpdateBodySection

    ###########################################################
    # Website BodySection - mutations - product List          #
    ##########################################################
    field :create_body_section_product_list, mutation: Mutations::Websites::CreateProductList
    field :update_body_section_product_list, mutation: Mutations::Websites::UpdateProductList

    ###########################################################
    # Website BodySection - mutations - banner              #
    ##########################################################
    field :create_body_section_banner, mutation: Mutations::Websites::CreateBanner
    field :update_body_section_banner, mutation: Mutations::Websites::UpdateBanner

    ###########################################################
    # Website BodySection - mutations - text and media        #
    ##########################################################
    field :create_body_section_text_media, mutation: Mutations::Websites::CreateTextMedia
    field :update_body_section_text_media, mutation: Mutations::Websites::UpdateTextMedia

    ###########################################################
    # Website BodySection - mutations - testimonial        #
    ##########################################################
    field :create_body_section_testimonial, mutation: Mutations::Websites::CreateTestimonial
    field :update_body_section_testimonial, mutation: Mutations::Websites::UpdateTestimonial

    ###########################################################
    # Website BodySection - mutations -personal testimonial        #
    ##########################################################
    field :create_personal_testimonial, mutation: Mutations::Websites::CreatePersonalTestimonial
    field :update_personal_testimonial, mutation: Mutations::Websites::UpdatePersonalTestimonial
    field :delete_personal_testimonial, mutation: Mutations::Websites::DeletePersonalTestimonial

    ###########################################################
    # Website BodySection - mutations - counter      #
    ##########################################################
    field :create_body_section_counter, mutation: Mutations::Websites::CreateCounterSection
    field :update_body_section_counter, mutation: Mutations::Websites::UpdateCounterSection

    ###########################################################
    # Website BodySection - mutations - free code      #
    ##########################################################
    field :create_body_section_free_code, mutation: Mutations::Websites::CreateFreeCodeSection
    field :update_body_section_free_code, mutation: Mutations::Websites::UpdateFreeCodeSection

    ###########################################################
    # Website BodySection - mutations - card     #
    ##########################################################
    field :create_body_section_card, mutation: Mutations::Websites::CreateCardSection
    field :update_body_section_card, mutation: Mutations::Websites::UpdateCardSection

    ###########################################################
    # Website BodySection - mutations - price     #
    ##########################################################
    field :create_body_section_price, mutation: Mutations::Websites::CreatePriceSection
    field :update_body_section_price, mutation: Mutations::Websites::UpdatePriceSection
    # field :create_body_section_price_card, mutation: Mutations::Websites::CreatePriceCard
    # field :update_body_section_price_card, mutation: Mutations::Websites::UpdatePriceCard
    field :delete_body_section_price_card, mutation: Mutations::Websites::DeletePriceCard
    # field :reorder_body_section_price_card, mutation: Mutations::Websites::ReorderPriceCard
    # field :create_body_section_price_card_item, mutation: Mutations::Websites::CreatePriceCardItem
    # field :update_body_section_price_card_item, mutation: Mutations::Websites::UpdatePriceCardItem
    field :delete_body_section_price_card_item, mutation: Mutations::Websites::DeletePriceCardItem
    # field :reorder_body_section_price_card_item, mutation: Mutations::Websites::ReorderPriceCardItem

    ###########################################################
    # Website BodySection - mutations - frequent_question     #
    ##########################################################
    field :create_body_section_faq, mutation: Mutations::Websites::CreateFrequentQuestionSection
    field :update_body_section_faq, mutation: Mutations::Websites::UpdateFrequentQuestionSection
    field :reorder_body_section_faq, mutation: Mutations::Websites::ReorderFrequentQuestionSection

    ###########################################################
    # Website button                                         #
    ##########################################################
    field :create_button, mutation: Mutations::Websites::CreateButton
    field :update_button, mutation: Mutations::Websites::UpdateButton
    field :delete_button, mutation: Mutations::Websites::DeleteButton
    field :update_button_style, mutation: Mutations::Websites::UpdateButtonStyle

    ## Website BodySection - mutations Media Element
    field :create_body_section_media, mutation: Mutations::Websites::CreateMediaSection
    field :update_body_section_media, mutation: Mutations::Websites::UpdateMediaSection

    ## Website BodySection - mutations Text Element
    field :create_body_section_text, mutation: Mutations::Websites::CreateTextSection
    field :update_body_section_text, mutation: Mutations::Websites::UpdateTextSection

    ## Website BodySection - mutations Button element
    field :create_body_section_button, mutation: Mutations::Websites::CreateButtonSection
    field :update_body_section_button, mutation: Mutations::Websites::UpdateButtonSection

    ## Website BodySection - mutations Email lead form element
    field :create_body_section_email_leads_form, mutation: Mutations::Websites::CreateEmailLeadsFormSection
    field :update_body_section_email_leads_form, mutation: Mutations::Websites::UpdateEmailLeadsFormSection
    
    # Delete Frequent question answer
    field :delete_frequent_question, mutation: Mutations::Websites::DeleteFrequentQuestion

    field :student_review_create, mutation: Mutations::Subscriptions::StudentReviewCreate
    field :student_review_update, mutation: Mutations::Subscriptions::StudentReviewUpdate
    field :course_duplicate, mutation: Mutations::Courses::CourseDuplicate

    field :create_custom_domain, mutation: Mutations::CreateCustomDomain

    field :generate_landing_page_short_url, mutation: Mutations::GenerateLandingPageShortUrl
    field :create_new_tenant, mutation: Mutations::CreateNewTenant
    field :generate_stripe_oauth_url, mutation: Mutations::GenerateStripeOauthUrl
    field :generate_stripe_oauth_token, mutation: Mutations::GenerateStripeOauthToken
  end
end
