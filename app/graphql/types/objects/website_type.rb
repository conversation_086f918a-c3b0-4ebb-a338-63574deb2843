module Types::Objects
  class WebsiteType < Types::BaseObject
    field :id, Integer, null: true
    field :title, String, null: false
    field :url, String, null: false
    field :state, String, null: false
    field :tenant_id, Integer, null: false
    field :main_color, String, null: false, resolver_method: :resolve_main_color
    field :header_code, String, null: true, resolver_method: :resolve_header_code
    field :header_text_font, String, null: true, resolver_method: :resolve_header_text_font
    field :header_logo_size, Integer, null: false, resolver_method: :resolve_header_logo_size
    field :header_color, String, null: false, resolver_method: :resolve_header_color
    field :header_text_color, String, null: false, resolver_method: :resolve_header_text_color
    field :header_transparent, Bo<PERSON>an, null: false, resolver_method: :resolve_header_transparent
    field :footer_copyright_label, String, null: false, resolver_method: :resolve_footer_copyright_label
    field :footer_color, String, null: false, resolver_method: :resolve_footer_color
    field :show_link_course, Boolean, null: false, resolver_method: :resolve_show_link_course
    field :show_link_pack, Boolean, null: false, resolver_method: :resolve_show_link_pack
    field :social_links, GraphQL::Types::JSON, null: true, resolver_method: :resolve_social_links
    field :header_links, GraphQL::Types::JSON, null: false, resolver_method: :resolve_header_links
    field :header_logo, Types::Objects::AttachmentType, null: true, resolver_method: :resolve_header_logo
    field :header_logo_sticky, Types::Objects::AttachmentType, null: true, resolver_method: :resolve_header_logo_sticky
    field :body_sections, [Types::Objects::Websites::BodySectionType], null: false
    field :website_footer, Types::Objects::WebsiteFooterType, null: true, description: "Footer configuration (only for homepage)"

    # Define resolver methods dynamically
    %i[
      main_color
      header_code
      header_text_font
      header_logo_size
      header_color
      header_text_color
      header_transparent
      footer_copyright_label
      footer_color
      show_link_course
      show_link_pack
      social_links
      header_links
      header_logo
      header_logo_sticky
    ].each do |method_name|
      define_method("resolve_#{method_name}") do
        home_page.public_send(method_name)
      end
    end

    private

    def home_page
      @home_page ||= if context[:affiliate].present? || context[:current_user]&.affiliate?
        affiliate = context[:affiliate] || context[:current_user]
        affiliate.websites.home_page.presence || object
      else
        context[:current_tenant].websites.home_page || object
      end
    end
  end
end
