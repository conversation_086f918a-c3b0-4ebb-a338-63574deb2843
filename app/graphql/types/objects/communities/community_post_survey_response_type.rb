module Types::Objects::Communities
  class CommunityPostSurveyResponseType < Types::BaseObject
    field :id, Integer, null: false
    field :community_post_survey_id, Integer, null: false
    field :user_id, Integer, null: true
    field :user, Types::Interfaces::UserType, null: true
    field :survey_answer_data, GraphQL::Types::JSON, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
