module Types
  class Objects::WebsiteFooterColumnType < Types::BaseObject
    field :id, Integer, null: false
    field :website_footer_id, Integer, null: false
    field :title, String, null: false
    field :links, GraphQL::Types::JSON, null: false
    field :position, Integer, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
