module Types
  class Objects::Courses::LessonSurveyType < Types::BaseObject
    implements Types::Interfaces::Courses::LessonContentType

    field :submit_button_text, String, null: true
    field :thank_you_message, String, null: true
    field :allow_anonymous, <PERSON><PERSON><PERSON>,  null: false
    field :skippable, <PERSON><PERSON><PERSON>, null: false

    field :questions, Types::Interfaces::Courses::SurveyQuestionType.connection_type, "The questions of this survey", null: false

    field :survey_responses, [Types::Objects::Subscriptions::SurveyResponseType], "responses of a survey", null: false
    field :survey_responses_count, Integer, "Total survey response count", null: false
    field :survey_answers, [Types::Objects::Subscriptions::SurveyAnswerType], "Total survey answers", null: false
    field :course, Types::Objects::Courses::PublicCourseType, null: false

    def survey_responses
      return @survey_responses_data if @survey_responses_data.present?

      @survey_responses_data = if(context[:subscription])
        object.subscription_survey_responses(context[:subscription])
      else
        object.survey_responses
      end
      @survey_responses_data
    end

    def survey_responses_count
      survey_responses.size
    end
  end
end
