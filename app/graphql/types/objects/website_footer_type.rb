module Types
  class Objects::WebsiteFooterType < Types::BaseObject
    field :id, Integer, null: false
    field :website_id, Integer, null: false

    # Footer template and styling
    field :template_name, String, null: false
    field :bg_text_icon_colour, String, null: false
    field :bg_colour, String, null: false
    field :bg_colour_visibility, <PERSON><PERSON><PERSON>, null: false
    field :bg_image, Types::Objects::AttachmentType, null: true
    field :bg_image_visibility, <PERSON><PERSON><PERSON>, null: false

    # Contact information
    field :contact_information_visibility, <PERSON><PERSON><PERSON>, null: false
    field :contact_information_title, String, null: true
    field :contact_information_address, String, null: true

    # Social networks
    field :social_networks_visibility, <PERSON><PERSON><PERSON>, null: false

    # Footer columns
    field :footer_columns, [Types::Objects::WebsiteFooterColumnType], null: false
    field :columns_visibility, <PERSON><PERSON>an, null: false

    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
