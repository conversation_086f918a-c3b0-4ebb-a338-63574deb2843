module Types
  class Objects::WebsiteFooterType < Types::BaseObject
    field :id, Integer, null: false
    field :website_id, Integer, null: false
    field :copyright_label, String, null: false
    field :footer_color, String, null: false
    field :description, String, null: true
    field :social_links, [GraphQL::Types::JSON], null: false
    field :contact_info, GraphQL::Types::JSON, null: false
    field :additional_links, GraphQL::Types::JSON, null: false
    field :layout_style, String, null: false
    field :show_social_links, <PERSON><PERSON>an, null: false
    field :show_contact_info, <PERSON><PERSON>an, null: false
    field :show_additional_links, <PERSON><PERSON><PERSON>, null: false
    field :newsletter_enabled, <PERSON><PERSON>an, null: false
    field :newsletter_title, String, null: true
    field :newsletter_description, String, null: true
    field :newsletter_button_text, String, null: false
    field :show_powered_by, <PERSON><PERSON><PERSON>, null: false
    field :custom_footer_text, String, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
