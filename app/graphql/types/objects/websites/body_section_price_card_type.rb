module Types
  class Objects::Websites::BodySectionPriceCardType < Types::BaseObject
    field :id, ID, null: true

    # Card content
    field :image, Types::Objects::AttachmentType, null: true
    field :background_color, String, null: true
    field :text_color, String, null: true
    field :font, String, null: true

    field :title, String, null: true
    field :title_size, String, null: true
    field :title_font_alignment, String, null: true
    field :title_font_bold, <PERSON><PERSON>an, null: true
    field :title_font_cursive, <PERSON><PERSON>an, null: true
    field :title_font_underlined, <PERSON><PERSON><PERSON>, null: true
    field :title_font_line_through, <PERSON><PERSON>an, null: true

    field :subtitle, String, null: true
    field :subtitle_size, String, null: true
    field :subtitle_font_alignment, String, null: true
    field :subtitle_font_bold, <PERSON><PERSON>an, null: true
    field :subtitle_font_cursive, <PERSON><PERSON>an, null: true
    field :subtitle_font_underlined, Boolean, null: true
    field :subtitle_font_line_through, <PERSON><PERSON><PERSON>, null: true

    field :description, String, null: true
    field :description_size, String, null: true
    field :description_font_alignment, String, null: true
    field :description_font_bold, Bo<PERSON>an, null: true
    field :description_font_cursive, <PERSON><PERSON><PERSON>, null: true
    field :description_font_underlined, <PERSON>olean, null: true
    field :description_font_line_through, Boolean, null: true

    field :icon_color, String, null: true
    field :visibility, Boolean, null: true

    # Button
    field :button_type, String, null: true
    field :button_text, String, null: true
    field :button_url, String, null: true
    field :button_visibility, Boolean, null: true

    field :position, Integer, null: true

    # Features relationship
    field :price_card_items, [Objects::Websites::BodySectionPriceCardItemType], null: true
  end
end
