module Types
  class Objects::Subscriptions::SurveyResponseType < Types::BaseObject
    field :id, Integer, null: false
    field :attempt_number, Integer, null: false
    field :subscription_id, Integer, null: true
    field :survey_id, Integer, null: false
    field :status, String, null: false
    field :survey_answers, [Types::Objects::Subscriptions::SurveyAnswerType], null: false
    field :subscription, Types::Objects::Subscriptions::SubscriptionType, null: true
    field :survey, Types::Objects::Courses::LessonSurveyType, null: false
    field :questions, Types::Interfaces::Courses::SurveyQuestionType.connection_type, "The questions of this survey attempt", null: false

    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
