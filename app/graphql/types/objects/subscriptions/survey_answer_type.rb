module Types
  class Objects::Subscriptions::SurveyAnswerType < Types::BaseObject
    field :id, Integer, null: true
    field :survey_response_id, Integer, null: false
    field :question_id, Integer, null: false
    field :answer, Types::Scalars::Subscriptions::AnswerContentType, null: true
    field :user, Types::Interfaces::UserType, null: false
    field :time_taken, Integer, null: false
  end
end
