module Mutations
  module CommunityMutations
    include Types::BaseInterface

    field :create_community, mutation: Mutations::Communities::CreateCommunity
    field :update_community, mutation: Mutations::Communities::UpdateCommunity
    field :delete_community, mutation: Mutations::Communities::DeleteCommunity
    field :reorder_communities, mutation: Mutations::Communities::ReorderCommunities

    # Community Posts
    field :create_community_post, mutation: Mutations::Communities::CreateCommunityPost
    field :update_community_post, mutation: Mutations::Communities::UpdateCommunityPost
    field :delete_community_post, mutation: Mutations::Communities::DeleteCommunityPost

    #Community Posts Response
    field :create_community_post_survey_response, mutation: Mutations::Communities::CreateCommunityPostSurveyResponse
  end
end
