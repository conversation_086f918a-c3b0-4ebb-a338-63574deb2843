module Mutations
  class Subscriptions::DeleteSubscription < BaseMutation
    # TODO: define return fields
    field :subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    # TODO: define arguments
    argument :subscription_id, Integer, required: true

    def resolve(subscription_id:)
      subscription = ::Subscriptions::Subscription.find(subscription_id)
      subscription.update!(disabled: true)
      {
        subscription: subscription
      }
    end
  end
end
