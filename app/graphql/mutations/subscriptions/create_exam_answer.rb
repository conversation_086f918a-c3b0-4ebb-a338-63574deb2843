module Mutations
  class Subscriptions::CreateExamAnswer < BaseMutation
    description "Creates exam answer"
    field :exam_answer, Types::Objects::Subscriptions::ExamAnswerType, null: false

    # TODO: define arguments
    argument :exam_answer_input, Types::Inputs::Subscriptions::ExamAnswerInputType, required: true

    def resolve(exam_answer_input:)
      input = exam_answer_input.to_kwargs
      answer = input.delete(:answer)
      time_taken = input.delete(:time_taken)
      exam_answer = ::Subscriptions::ExamAnswer.find_or_initialize_by(**input)
      exam_answer.answer = answer
      exam_answer.time_taken = time_taken
      exam_answer.save!
      {
        exam_answer: exam_answer
      }
    end
  end
end
