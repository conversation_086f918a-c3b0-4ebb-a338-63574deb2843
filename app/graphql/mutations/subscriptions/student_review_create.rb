# frozen_string_literal: true

module Mutations::Subscriptions
  class StudentReviewCreate < Mutations::BaseMutation
    description "Creates a new student_review"

    field :student_review, Types::Objects::Subscriptions::StudentReviewType, null: false

    argument :subscription_id, Integer, required: true
    argument :student_review_input, Types::Inputs::Subscriptions::StudentReviewInputType, required: true

    def resolve(subscription_id:, student_review_input:)
      subscription = context[:current_user].subscriptions.find(subscription_id)
      student_review = subscription.build_student_review(**student_review_input)
      raise GraphQL::ExecutionError.new "Error creating student_review", extensions: subscriptions_student_review.errors.to_hash unless student_review.save

      { student_review: }
    end
  end
end
