module Mutations
  class Subscriptions::CreateProgress < BaseMutation
    description "Creates progress"
    field :lesson, Types::Objects::Courses::LessonType, null: false

    argument :progress_input, Types::Inputs::Subscriptions::ProgressInputType, required: true

    def resolve(progress_input:)
      progress = context[:current_tenant].progresses.new(**progress_input)
      progress.save!
      context[:subscription] = progress.subscription
      {
        lesson: progress.lesson
      }
    end
  end
end
