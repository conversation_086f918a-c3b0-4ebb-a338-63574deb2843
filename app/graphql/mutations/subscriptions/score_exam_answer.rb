module Mutations
  class Subscriptions::ScoreExamAnswer < BaseMutation
    description "ALlows an instructor to score an exam answer"
    field :exam_answer, Types::Objects::Subscriptions::ExamAnswerType, null: false

    # TODO: define arguments
    argument :id, Integer, required: true
    argument :answer_score_input, Types::Inputs::Subscriptions::AnswerScoreInputType, required: true

    def resolve(id:, answer_score_input:)
      exam_answer = ::Subscriptions::ExamAnswer.find(id)
      exam_answer.score!(**answer_score_input, scored_by: context[:current_user])
      {
        exam_answer: exam_answer
      }
    end
  end
end


