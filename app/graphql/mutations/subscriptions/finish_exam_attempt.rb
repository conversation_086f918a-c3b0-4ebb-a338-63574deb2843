module Mutations
  class Subscriptions::FinishExamAttempt < ::Mutations::BaseMutation
    description "Finish exam attempt"
    field :exam_attempt, Types::Objects::Subscriptions::ExamAttemptType, null: false

    # TODO: define arguments
    argument :id, Integer, "The ID of the exam attempt to be updated", required: true

    def resolve(id:)
      exam_attempt = context[:current_tenant].exam_attempts.find_by(id: id)
      exam_attempt.finish!

      if exam_attempt[:status].eql? 'passed'
        message_type = :student_passed_exam 
      else
        message_type = :student_failed_exam
      end
      ::Triggers::Trigger.apply_triggers(message_type, exam_attempt.subscription.student)

      UserMessagesManager.send_message_to_user(exam_attempt.subscription.student, 
                                                message_type, 
                                                template_variables: { 
                                                  course_name: exam_attempt.subscription.course.name,
                                                  exam_name: exam_attempt.exam.lesson.title })

      {
        exam_attempt: exam_attempt
      }
    end
  end
end


