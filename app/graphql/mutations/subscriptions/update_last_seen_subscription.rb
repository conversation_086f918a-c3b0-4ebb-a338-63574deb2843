module Mutations
    class Subscriptions::UpdateLastSeenSubscription < BaseMutation
      # TODO: define return fields
      field :subscription, Types::Objects::Subscriptions::SubscriptionType, null: false
      # TODO: define arguments
      argument :last_seen, GraphQL::Types::JSON, required: true
      argument :id, Integer, required: true
  
      def resolve(id:, last_seen:)
        subscription = ::Subscriptions::Subscription.find(id)
        begin
            subscription.update!(last_seen:)
        rescue StandardError => e
            raise GraphQL::ExecutionError.new 'Error updating subscriptions'
        end
        {
          subscription: subscription
        }
      end
    end
  end