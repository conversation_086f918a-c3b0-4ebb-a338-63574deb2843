module Mutations
  class Subscriptions::CreateCourseSubscription < BaseMutation
    # TODO: define return fields
    field :course_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    # TODO: define arguments
    argument :student_id, Integer, required: true
    argument :course_id, Integer, required: true

    def resolve(student_id:, course_id:)
      course = ::Courses::Course.find(course_id)
      student = Student.find(student_id)
      course_subscription = course.add_student!(student, "admin_add", context[:current_user])

      UserMessagesManager.send_message_to_user(student, :admin_added_student, template_variables: { 
                                                        product_name: course.name,
                                                        product_type: 'curso',
                                                        admin_name: context[:current_user].full_name,
                                                        url_text: 'Ingresar al curso'}) 
      ::Triggers::Trigger.apply_triggers('admin_added_student', student)

      {
        course_subscription: course_subscription
      }
    end
  end
end
