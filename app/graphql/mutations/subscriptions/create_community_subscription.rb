module Mutations
  class Subscriptions::CreateCommunitySubscription < BaseMutation
    field :community_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    argument :student_id, Integer, required: true
    argument :community_id, Integer, required: true

    def resolve(student_id:, community_id:)
      community = ::Communities::Community.find(community_id)
      student = Student.find(student_id)
      community_subscription = community.add_student!(student, "admin_add", context[:current_user])

      # UserMessagesManager.send_message_to_user(student, :admin_added_student, template_variables: { 
      #                                                   product_name: community.name,
      #                                                   product_type: 'curso',
      #                                                   admin_name: context[:current_user].full_name,
      #                                                   url_text: 'Ingresar al curso'}) 

      {
        community_subscription: community_subscription
      }
    end
  end
end
