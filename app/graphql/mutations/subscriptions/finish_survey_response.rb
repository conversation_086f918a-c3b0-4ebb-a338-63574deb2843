module Mutations
  class Subscriptions::FinishSurveyResponse < ::Mutations::BaseMutation
    field :survey_response, Types::Objects::Subscriptions::SurveyResponseType, null: false

    argument :id, Integer, "The ID of the survey response to be updated", required: true

    def resolve(id:)
      survey_response = context[:current_tenant].survey_responses.find_by(id: id)
      survey_response.finish!

      {
        survey_response: survey_response
      }
    end
  end
end
