module Mutations
  class Subscriptions::UnsubscribeToCommunity < BaseMutation
    field :subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    argument :community_id, Integer, required: true
    argument :student_id, Integer, required: false

    def resolve(community_id:, student_id:)
      if context[:current_user].is_a?(Student)
        subscription = context[:current_user].community_subscriptions.find_by(community_id:)
      else
        subscription = context[:current_tenant].community_subscriptions.find_by(community_id:, student_id:)
      end
      subscription.update!(disabled: true)
      {
        subscription: subscription
      }
    end
  end
end
