module Mutations
  class Subscriptions::CreateExamAttempt < BaseMutation
    description "Creates exam attempt"
    field :exam_attempt, Types::Objects::Subscriptions::ExamAttemptType, null: false

    argument :exam_attempt_input, Types::Inputs::Subscriptions::ExamAttemptInputType, required: true

    def resolve(exam_attempt_input:)
      exam_attempt = ::Subscriptions::ExamAttempt.where(status: :initiated, exam_id: exam_attempt_input[:exam_id], subscription_id: exam_attempt_input[:subscription_id], tenant_id: context[:current_tenant].id ).last
      exam_attempt = context[:current_tenant].exam_attempts.create!(**exam_attempt_input) unless exam_attempt
      {
        exam_attempt: exam_attempt
      }
    end
  end
end
