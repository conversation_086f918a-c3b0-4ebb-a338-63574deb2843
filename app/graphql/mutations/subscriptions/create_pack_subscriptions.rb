module Mutations
  class Subscriptions::CreatePackSubscriptions < BaseMutation
    # TODO: define return fields
    field :pack_subscriptions, [Types::Objects::Subscriptions::SubscriptionType], null: false
    # field :pack_subscription, [Types::Objects::Courses::CourseType], null: false

    # TODO: define arguments
    argument :student_id, Integer, required: true
    argument :pack_id, Integer, required: true

    def resolve(student_id:, pack_id:)
      pack = ::Courses::Pack.find(pack_id)
      student = Student.find(student_id)
      pack_subscriptions = pack.add_student!(student, "admin_add", context[:current_user])

      UserMessagesManager.send_message_to_user(student, :admin_added_student, template_variables: { 
        product_name: pack.name,
        product_type: 'pack',
        admin_name: context[:current_user].full_name,
        url_text: 'Ingresar al Pack' } ) 

      ::Triggers::Trigger.apply_triggers('admin_added_student', student)

      {
        pack_subscriptions: pack_subscriptions
      }
    end
  end
end
