# frozen_string_literal: true

module Mutations::Subscriptions
  class StudentReviewUpdate < Mutations::BaseMutation

    field :student_review, Types::Objects::Subscriptions::StudentReviewType, null: false

    argument :subscription_id, Integer, required: true
    argument :student_review_input, Types::Inputs::Subscriptions::StudentReviewInputType, required: true

    def resolve(subscription_id:, student_review_input:)
      subscription = context[:current_user].subscriptions.find(subscription_id)
      student_review = subscription.student_review
      raise GraphQL::ExecutionError, student_review.errors.full_messages.join(', ') unless student_review.update(**student_review_input)
      { student_review: }
    end
  end
end
