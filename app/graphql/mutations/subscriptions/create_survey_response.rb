module Mutations
  class Subscriptions::CreateSurveyResponse < BaseMutation
    field :survey_response, Types::Objects::Subscriptions::SurveyResponseType, null: false

    argument :survey_response_input, Types::Inputs::Subscriptions::SurveyResponseInputType, required: true

    def resolve(survey_response_input:)
      survey_response = ::Subscriptions::SurveyResponse.where(status: :initiated, survey_id: survey_response_input[:survey_id], subscription_id: survey_response_input[:subscription_id], tenant_id: context[:current_tenant].id ).last
      survey_response = context[:current_tenant].survey_responses.create!(**survey_response_input) unless survey_response
      {
        survey_response: survey_response
      }
    end
  end
end
