module Mutations
    class Subscriptions::DeletePackSubscriptions < BaseMutation
      field :pack_subscriptions, [Types::Objects::Subscriptions::SubscriptionType], null: false
  
      argument :subscriptions_id, [Integer], required: true
      def resolve(subscriptions_id:)      
        begin
            pack_subscriptions = []
            subscriptions_id.each do |subscription_id| 
                subscription = ::Subscriptions::Subscription.find(subscription_id)
                subscription.update!(disabled: true)
                pack_subscriptions.push(subscription)
            end
        rescue StandardError => e
            raise GraphQL::ExecutionError.new 'Error updating subscriptions'
        end
        {
            pack_subscriptions: pack_subscriptions
        }
      end
    end
  end
  