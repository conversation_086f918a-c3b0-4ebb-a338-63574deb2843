module Mutations
  class Subscriptions::CreateSurveyAnswer < BaseMutation
    description "Creates survey answer"
    field :survey_answer, Types::Objects::Subscriptions::SurveyAnswerType, null: false

    # TODO: define arguments
    argument :survey_answer_input, Types::Inputs::Subscriptions::SurveyAnswerInputType, required: true

    def resolve(survey_answer_input:)
      input = survey_answer_input.to_kwargs
      answer = input.delete(:answer)
      time_taken = input.delete(:time_taken)
      survey_answer = ::Subscriptions::SurveyAnswer.find_or_initialize_by(**input)
      survey_answer.answer = answer
      survey_answer.time_taken = time_taken
      survey_answer.save!
      {
        survey_answer: survey_answer
      }
    end
  end
end
