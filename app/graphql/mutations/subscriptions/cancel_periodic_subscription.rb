module Mutations
    class Subscriptions::CancelPeriodicSubscription < BaseMutation
      # TODO: define return fields
      field :purchase, Types::Objects::Purchases::ProductPurchaseType, null: false

      # TODO: define arguments
      argument :provider_id, ID, required: false
      argument :product_purchase_id, ID, required: false
  
      def resolve(provider_id: ,product_purchase_id:)
        purchase = ::Purchases::ProductPurchase.find(product_purchase_id)
        subscriptions = purchase.subscriptions

        status = purchase.provider_handler.cancel_subscription(purchase)
        if status.eql?"cancelled"
          subscriptions.update_all(status:)
          UserMessagesManager.send_message_to_user(purchase.user, :student_subscription_cancel, template_variables: { product_name: purchase.product.name })
        end

        { purchase: }
      end
    end
  end
  