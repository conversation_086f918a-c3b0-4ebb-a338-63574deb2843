module Mutations
  class UpdateWebsiteFooter < BaseMutation
    description "Updates a website footer"

    field :website_footer, Types::Objects::WebsiteFooterType, null: false

    argument :website_id, Integer, required: true, description: "ID of the homepage website"
    argument :footer_input, Types::Inputs::WebsiteFooterInputType, required: true

    def resolve(website_id:, footer_input:)
      website = context[:current_tenant].websites.find(website_id)
      
      unless website.homepage?
        raise GraphQL::ExecutionError, "Footer can only be updated for homepage websites"
      end
      
      website_footer = website.footer || website.ensure_footer!
      
      raise GraphQL::ExecutionError.new "Error updating website footer", extensions: website_footer.errors.to_hash unless website_footer.update(**footer_input)

      { website_footer: website_footer }
    end
  end
end
