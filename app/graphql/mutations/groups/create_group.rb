module Mutations
  class Groups::CreateGroup < BaseMutation
    description "Creates a new group"
    field :group, Types::Objects::GroupType, null: false

    argument :group_input, Types::Inputs::GroupInputType, required: true

    def resolve(group_input:)
      group = ::Groups::Group.new(**group_input, tenant: context[:current_tenant])
      group.save!
      {
        group: group
      }
    end
  end
end
