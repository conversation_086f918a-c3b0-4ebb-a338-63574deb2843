module Mutations
  module Groups
    class UpdateGroup < ::Mutations::BaseMutation
      field :group, Types::Objects::GroupType, null: false

      argument :id, Integer, "The ID of the module to be updated", required: true
      argument :group_input, Types::Inputs::GroupInputType, "The group data to be updated", required: true

      def resolve(id:, group_input:)
        group = ::Groups::Group.find(id)
        group.update!(**group_input)
        { group: group }
      end
    end
  end
end
