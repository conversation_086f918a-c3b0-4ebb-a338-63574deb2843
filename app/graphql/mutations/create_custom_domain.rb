module Mutations
  class CreateCustomDomain < BaseMutation
    include NetlifyConcern

    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: false

    argument :domain, String, required: true

    def resolve(domain:)
      checker = DnsChecker.new(domain)
      if checker.check_a_record
        response = set_domain_alias_to_netlify(domain)
        if response[:success]
          custom_domain = CustomDomain.find_or_initialize_by(domain:)
          if custom_domain.save
            { success: true, errors: [] }
          else
            { success: false, errors: custom_domain.errors.full_messages }
          end
        else
          { success: false, errors: [response[:error]] }
        end

      else
        { success: false, errors: ["Domain not Configired"] }
      end
    end
  end
end
