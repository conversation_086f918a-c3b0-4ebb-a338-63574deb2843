module Mutations::Centralized
  class LoginAsUser < Mutations::BaseMutation
    argument :email, String, required: true
    argument :role, String, required: false
    argument :tenant_id, ID, required: false

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true
    field :headers, GraphQL::Types::JSON, null: true

    def resolve(email:, role: nil, tenant_id: nil)
      object_class = if role.present?
        role.constantize
      else
        User
      end

      user = if tenant_id.present?
        object_class.where("LOWER(email) = ?", email.downcase).where(tenant_id:).first
      else
        object_class.where("LOWER(email) = ?", email.downcase).first
      end

      unless user.present? && context[:current_user]&.email&.strip&.downcase == email.strip.downcase
        return { success: false, errors: ["User not found"], headers: {} }
      end

      login_token = user.create_new_auth_token

      { success: true, headers: login_token, errors: [] }
    rescue => e
      { success: false, errors: [e.message] }
    end
  end
end
