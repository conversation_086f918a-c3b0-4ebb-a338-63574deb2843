module Mutations
  module Tags
    class UpdateTag < ::Mutations::BaseMutation
      field :tag, Types::Objects::TagType, null: false

      argument :id, Integer, "The ID of the tag to be updated", required: true
      argument :tag_input, Types::Inputs::TagInputType, "The tag data to be updated", required: true

      def resolve(id:, tag_input:)
        tag = ::Tag.find(id)
        tag.update!(**tag_input)
        { tag: tag }
      end
    end
  end
end
