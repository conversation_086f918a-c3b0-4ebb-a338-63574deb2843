module Mutations
  module Settings
    class UpdateGlobalSettings < ::Mutations::BaseMutation
      field :settings, Types::Objects::GlobalSettingsType, null: false

      argument :settings_input, Types::Inputs::SettingsInputType, "The global setting data to be updated", required: true

      def resolve(settings_input:)
        pp settings_input
        context[:current_tenant].update!(**settings_input)
        { settings: context[:current_tenant] }
      end
    end
  end
end
