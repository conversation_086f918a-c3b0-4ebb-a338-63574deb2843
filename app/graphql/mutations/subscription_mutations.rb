module Mutations
  module SubscriptionMutations
    include Types::BaseInterface

    field :create_course_subscription, mutation: Mutations::Subscriptions::CreateCourseSubscription
    field :create_community_subscription, mutation: Mutations::Subscriptions::CreateCommunitySubscription
    field :cancel_periodic_subscription, mutation: Mutations::Subscriptions::CancelPeriodicSubscription
    field :create_pack_subscriptions, mutation: Mutations::Subscriptions::CreatePackSubscriptions
    field :create_progress, mutation: Mutations::Subscriptions::CreateProgress

    field :update_last_seen_subscription, mutation: Mutations::Subscriptions::UpdateLastSeenSubscription
    field :delete_subscription, mutation: Mutations::Subscriptions::DeleteSubscription
    field :delete_pack_subscriptions, mutation: Mutations::Subscriptions::DeletePackSubscriptions
    field :subscribe_to_course, mutation: Mutations::Students::Groups::SubscribeToCourse
    field :subscribe_to_community, mutation: Mutations::Students::Groups::SubscribeToCommunity
    field :unsubscribe_to_community, mutation: Mutations::Subscriptions::UnsubscribeToCommunity
    field :subscribe_to_pack, mutation: Mutations::Students::Groups::SubscribeToPack
  end
end 