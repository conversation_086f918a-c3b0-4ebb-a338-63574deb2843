module Mutations
    class Emails::UpdateSignature < BaseMutation
      field :signature, Types::Objects::Emails::SignatureType, null: false

      argument :postmark_id, String, required: true
      argument :signature_input, Types::Inputs::Emails::SignatureInputType, required: true

      def resolve(postmark_id:, signature_input:)
        account = Postmark::AccountApiClient.new('************************************')
        response = account.update_sender(postmark_id, { 
                                             FromEmail: signature_input[:email], 
                                             Name: signature_input[:name], 
                                             ReplyToEmail: signature_input[:reply] })

        signature = ::Emails::Signature.find_by(postmark_id:)
        signature.update(**signature_input)
        { signature: signature }
        rescue ::Postmark::ApiInputError => e
            raise Sabiorealm::Error, e.message
        end
    end
  end


  