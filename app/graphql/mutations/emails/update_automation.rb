module Mutations
    class Emails::UpdateAutomation < BaseMutation
      field :automation, Types::Objects::Emails::AutomationType, null: false

      argument :id, ID, required: true
      argument :automation_input, Types::Inputs::Emails::AutomationInputType, required: true

      def resolve(id:,automation_input:)
        automation = ::Emails::Automation.find(id)
        automation.update!(**automation_input)
        
        { automation: automation }
      end
    end
end
