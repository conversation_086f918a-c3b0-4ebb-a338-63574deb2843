module Mutations
    class Emails::UpdateTemplate < BaseMutation
      field :template, Types::Objects::Emails::TemplateType, null: false

      argument :id, ID, required: true
      argument :template_input, Types::Inputs::Emails::TemplateInputType, required: true

      def resolve(id:, template_input:)
        template = ::Emails::Template.find(id)
        template.update(**template_input)
        
        { template: template }
      end
    end
end


  