module Mutations
    class Emails::UpdateEmail < BaseMutation
      field :email, Types::Objects::Emails::EmailType, null: false

      argument :id, ID, required: true
      argument :email_input, Types::Inputs::Emails::EmailInputType, required: false
      argument :automation_input, Types::Inputs::Emails::AutomationInputType, required: false

      def resolve(id:, email_input:, automation_input:)
        ActiveRecord::Base.transaction do
          email = ::Emails::Email.find(id)
          email.update(**email_input) if email_input.present?
          automation_id = email.automation&.id
          current_automation = ::Emails::Automation.find_by(**automation_input)
          unless current_automation&.id == automation_id
            current_automation.update(email_id: email.id)
            if automation_id.present?
              automation = ::Emails::Automation.find(automation_id) 
              automation.update(email_id: nil)
            end 
          end
          { email: email }
        end
      end
    end
  end


  