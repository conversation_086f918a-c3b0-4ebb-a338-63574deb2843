module Mutations
  class Emails::ResendEmailSignature < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    argument :postmark_id, String, required: true

    def resolve(postmark_id:)
      account = Postmark::AccountApiClient.new('************************************')
      begin
        response = account.resend_sender_confirmation(postmark_id)

        signature = ::Emails::Signature.find_by(postmark_id:)
        signature.confirm_signature
        raise GraphQL::ExecutionError.new "Error sending email" if response[:error_code] != 0 
        {  success: response[:error_code] == 0 }
      rescue Postmark::ApiInputError => e
        signature ||= ::Emails::Signature.find_by(postmark_id:)
        if e.message.include?("already been confirmed")
          signature.confirm_signature if signature
          { success: true }
        else
          raise GraphQL::ExecutionError.new("Postmark error: #{e.message}")
        end
      end
    end
  end
end
