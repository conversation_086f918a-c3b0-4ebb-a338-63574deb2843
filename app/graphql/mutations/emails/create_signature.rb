module Mutations
    class Emails::CreateSignature < BaseMutation
      field :signature, Types::Objects::Emails::SignatureType, null: false
      argument :signature_input, Types::Inputs::Emails::SignatureInputType, required: true
  
      def resolve(signature_input:)
        account = Postmark::AccountApiClient.new('************************************')
        response = account.create_sender({ FromEmail: signature_input[:email], 
                                           Name: signature_input[:name], 
                                           ReplyToEmail: signature_input[:reply],
                                           ConfirmationPersonalNote: 'Sabionet - confirma tu correo electronico,'})
        signature_hash = signature_input.to_h
        signature_hash[:postmark_id] = response[:id]
        signature_hash[:confirmed] = response[:confirmed]
        signature = ::Emails::Signature.new(**signature_hash)
        signature.save!
        { signature: signature }
        
      rescue ::Postmark::ApiInputError => e
        raise Sabiorealm::Error, e.message
      end
    end
end