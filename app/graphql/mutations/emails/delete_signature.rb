module Mutations
    class Emails::DeleteSignature < BaseMutation
      field :signature, Types::Objects::Emails::SignatureType, null: false
      argument :postmark_id, String, required: true
  
      def resolve(postmark_id:)
        account = Postmark::AccountApiClient.new('************************************')
        response = account.delete_sender(postmark_id)
        signature = ::Emails::Signature.find_by(postmark_id:)
        signature.destroy!
        { signature: signature }
        rescue ::Postmark::ApiInputError => e
            raise Sabiorealm::Error, e.message
        end
    end
  end
