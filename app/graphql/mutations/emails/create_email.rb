module Mutations
    class Emails::CreateEmail < BaseMutation
      field :email, Types::Objects::Emails::EmailType, null: false

      argument :automation_input, Types::Inputs::Emails::AutomationInputType, required: true
      argument :email_input, Types::Inputs::Emails::EmailInputType, required: true
  
      def resolve(email_input:, automation_input:)
        ActiveRecord::Base.transaction do
          email = ::Emails::Email.create!(**email_input)
          unless automation_input.when.nil?
            automation = ::Emails::Automation.find_by(**automation_input)
            automation.update(email_id: email.id)
          end
          
          { email: email }
        end
      end
    end
end
