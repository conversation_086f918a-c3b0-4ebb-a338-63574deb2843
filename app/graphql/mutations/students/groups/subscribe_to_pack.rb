module Mutations
  class Students::Groups::SubscribeToPack < BaseMutation
    # TODO: define return fields
    field :new_subscriptions, [Types::Objects::Subscriptions::SubscriptionType], null: false

    # TODO: define arguments
    argument :pack_id, Integer, required: true

    def resolve(pack_id:)
      student = context[:current_user]
      pack = ::Courses::Pack.find(pack_id)
      common_group_ids = pack.group_students.where(student: student).pluck(:group_id)
      raise GraphQL::ExecutionError, "This student doesn't have access to this pack" unless common_group_ids.any?

      new_subscriptions = nil
      common_group_ids.each do |cgid|
        new_subscriptions = pack.add_student!(student, "pack_group_select", context[:current_user], group_id: cgid)
      end
      {
        new_subscriptions: new_subscriptions
      }
    end
  end
end
