module Mutations
  class Students::Groups::SubscribeToCourse < BaseMutation
    # TODO: define return fields
    field :new_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    # TODO: define arguments
    argument :course_id, Integer, required: true

    def resolve(course_id:)
      student = context[:current_user]
      course = ::Courses::Course.find(course_id)
      common_group_ids = course.group_students.where(student: student).pluck(:group_id)
      raise GraphQL::ExecutionError, "This student doesn't have access to this course" unless common_group_ids.any?

      new_subscription = nil
      common_group_ids.each do |cgid|
        new_subscription = course.add_student!(student, "course_group_select", context[:current_user], group_id: cgid)
      end
      {
        new_subscription: new_subscription
      }
    end
  end
end
