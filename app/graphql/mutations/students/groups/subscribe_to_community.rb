module Mutations
  class Students::Groups::SubscribeToCommunity < BaseMutation
    field :new_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    argument :community_id, Integer, required: true

    def resolve(community_id:)
      student = context[:current_user]
      community =  context[:current_tenant].communities.find(community_id)
        new_subscription = community.add_student!(student, "community_select", context[:current_user])
      {
        new_subscription: new_subscription
      }
    end
  end
end
