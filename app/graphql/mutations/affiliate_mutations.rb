module Mutations
  module AffiliateMutations
    include Types::BaseInterface

    field :update_affiliate, mutation: Mutations::Affiliates::UpdateAffiliate
    field :update_affiliate_request, mutation: Mutations::Affiliates::UpdateAffiliateRequest
    field :invite_affiliate, mutation: Mutations::Affiliates::InviteAffiliate
    field :validate_affiliate_invitation, mutation: Mutations::Affiliates::ValidateAffiliateInvitation
    field :affiliate_course_request, mutation: Mutations::Affiliates::AffiliateCourseRequest
    field :unsubscribe_affiliate_course, mutation: Mutations::Affiliates::UnsubscribeAffiliateCourse
    field :create_affiliate_stripe_connect, mutation: Mutations::Affiliates::Payments::CreateAffiliateStripeConnect
    field :get_affiliate_stripe_connect_setup_url, mutation: Mutations::Affiliates::Payments::GetAffiliateStripeConnectSetupUrl
    field :affiliate_stripe_connect_dashboard_url, mutation: Mutations::Affiliates::Payments::AffiliateStripeConnectDashboardUrl
    field :create_affiliate_link, mutation: Mutations::Affiliates::CreateAffiliateLink

    field :affiliate_product_purchase_approve, mutation: Mutations::Affiliates::Purchases::AffiliateProductPurchaseApprove
    field :affiliate_product_purchase_update, mutation: Mutations::Affiliates::Purchases::AffiliateProductPurchaseUpdate

    field :update_affiliate_refund_request, mutation: Affiliates::Refunds::UpdateAffiliateRefundRequest
    field :create_notification_player, mutation: Mutations::Affiliates::CreateNotificationPlayer
    field :login_as_affiliate, mutation: Mutations::Affiliates::LoginAsAffiliate
  end
end
