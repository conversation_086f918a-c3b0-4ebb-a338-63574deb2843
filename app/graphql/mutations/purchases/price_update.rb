# frozen_string_literal: true

module Mutations
  class Purchases::PriceUpdate < BaseMutation
    description "Updates a price by id"

    field :price, Types::Objects::Purchases::PriceType, null: false

    argument :id, ID, required: true
    argument :price_input, Types::Inputs::Purchases::PriceInputType, required: true

    def resolve(id:, price_input:)
      purchases_price = ::Purchases::Price.find(id)
      course = purchases_price.product
      raise GraphQL::ExecutionError.new "Error updating price", extensions: purchases_price.errors.to_hash unless purchases_price.update(**price_input)

      #actualizar el precio de la licencia asociada al curso
      if price_input.currency.eql? 'USD' && (purchases_price.product_type.eql? 'Courses::Course') && course.licenses.exists?
        license_price = ::Marketplaces::License.find_by(licensed_by_course: course.id).prices.last
        license_price.update( price: price_input.price, discounted_price: price_input.discounted_price ) if license_price.present?
      end
    
      { price: purchases_price }
    end
  end
end
