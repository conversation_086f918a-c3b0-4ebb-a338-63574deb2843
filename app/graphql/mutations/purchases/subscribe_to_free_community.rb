module Mutations
  class Purchases::SubscribeToFreeCommunity < BaseMutation
    field :new_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    argument :community_id, Integer, required: true
    argument :price_id, Integer, required: false
    argument :coupon_code, String, required: false

    def resolve(community_id:, price_id: nil, coupon_code: nil)
      student = context[:current_user]
      community = ::Communities::Community.visible.find(community_id)

      raise GraphQL::ExecutionError, "This community is not free" unless community.is_free

      new_subscription = community.add_student!(student, "free_community_select", student)
      # UserMessagesManager.send_message_to_user(context[:current_tenant].super_admin, :admin_new_free_product, template_variables: { product_name: community.name, student_name: student.first_name },
      #                                                                                                         extras: { avatar_data: student.avatar_url , student_id: student.id })
      {
        new_subscription: new_subscription
      }
    end
  end
end
