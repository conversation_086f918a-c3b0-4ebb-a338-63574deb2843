# frozen_string_literal: true

module Mutations
  class Purchases::PriceDelete < BaseMutation
    description "Deletes a price by ID"

    field :price, Types::Objects::Purchases::PriceType, null: false

    argument :id, ID, required: true

    def resolve(id:)
      purchases_price = ::Purchases::Price.find(id)
      raise GraphQL::ExecutionError.new "Error deleting price", extensions: purchases_price.errors.to_hash unless purchases_price.destroy

      { price: purchases_price }
    end
  end
end
