# frozen_string_literal: true

module Mutations
  class Purchases::ProductPurchaseApprove < BaseMutation
    description "Approves a product_purchase by id"

    field :product_purchase, Types::Objects::Purchases::ProductPurchaseType, null: false

    argument :id, ID, required: true

    def resolve(id:)
      purchases_product_purchase = ::Purchases::ProductPurchase.find(id)
      purchases_product_purchase.approve! unless purchases_product_purchase.approved?
        
      { product_purchase: purchases_product_purchase }
    rescue ActiveRecord::RecordNotFound => e
      raise GraphQL::ExecutionError.new "product_purchase not exist", extensions: {error: e.message}
    end
  end
end
