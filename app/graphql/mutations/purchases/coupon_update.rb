module Mutations
    class Purchases::CouponUpdate < BaseMutation
      description "Updates a coupon by id"
  
      argument :id, ID, required: true
      argument :coupon_input, Types::Inputs::Purchases::CouponInputType, required: true
      
      field :coupon, Types::Objects::Purchases::CouponType, null: true
      field :errors, [String], null: false 

      def resolve(id:, coupon_input:)
        coupon_hash = coupon_input.to_h
        coupon_products_input = coupon_hash.delete(:products)

        coupon = ::Purchases::Coupon.find(id)
        if coupon.update(**coupon_hash)
          array = []
          coupon_products_input.each do |coupon_product_input|
            coupon_product = ::Purchases::CouponProduct.find_or_create_by(product_id: coupon_product_input[:product_id], 
                                              coupon_id: coupon.id, product_type: coupon_product_input[:product_type])
            array.push(coupon_product)
          end
          coupon.coupon_products = array

          {
              coupon: coupon,
              errors: []
          }
        else
          {
              coupon: nil,
              errors: coupon.errors.full_messages
          }
        end
      end
    end
  end
  