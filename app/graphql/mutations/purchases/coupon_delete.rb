module Mutations
    class Purchases::CouponDelete < BaseMutation
      description "Deletes a coupon by ID"
  
      argument :id, ID, required: true

      field :id, ID, null: true

      def resolve(id:)
        coupon = ::Purchases::Coupon.find(id)
        raise GraphQL::ExecutionError.new "Error deleting coupon", extensions: coupon.errors.to_hash unless coupon.destroy
  
        { id: id }
      end
    end
  end
  