# frozen_string_literal: true

module Mutations
  class Purchases::ProductPurchaseVerify < BaseMutation
    description "Updates a transaction by its provider id"

    field :purchase, Types::Objects::Purchases::ProductPurchaseType, null: false

    argument :provider, Types::Enums::Purchases::ProductPurchaseProviderType, required: true
    argument :provider_id, ID, required: true
    argument :verify_data, GraphQL::Types::JSON, required: false

    def resolve(provider:, provider_id:, verify_data: nil)
      MultiTenantSupport::Current.tenant_account = nil
      MultiTenantSupport.turn_off_protection

      purchase = ::Purchases::ProductPurchase.find_by(provider_id: provider_id)
      tenant = purchase.tenant

      MultiTenantSupport.under_tenant tenant do
        begin
          purchase.verify!(verify_data) unless purchase.approved? # Siempre intenta verificar la transacción, al menos que ya esté aprobada
          { purchase: purchase }
        rescue StandardError => e
          raise GraphQL::ExecutionError.new "Error verifying purchase", extensions: e.message
        end
      end
    end
  end
end
