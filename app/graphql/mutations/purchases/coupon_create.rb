module Mutations
    class Purchases::CouponCreate < BaseMutation
      description "Creates a new coupon"
  
      argument :coupon_input, Types::Inputs::Purchases::CouponInputType, required: true
      
      field :coupon, Types::Objects::Purchases::CouponType, null: true
      field :errors, [String], null: false 

      def resolve(coupon_input:)
        coupon_hash = coupon_input.to_h
        coupon_products = coupon_hash.delete(:products)
        
        coupon = ::Purchases::Coupon.new(**coupon_hash)
        
        if coupon.save
            coupon_products.each do |coupon_product|  
              ::Purchases::CouponProduct.create(product_id: coupon_product[:product_id], 
                                                coupon_id: coupon.id, product_type: coupon_product[:product_type])
            end
            {
                coupon: coupon,
                errors: []
            }
        else
            {
                coupon: nil,
                errors: coupon.errors.full_messages
            }
        end
      end
    end
  end
  