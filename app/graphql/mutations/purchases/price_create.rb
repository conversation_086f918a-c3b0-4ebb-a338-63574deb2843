# frozen_string_literal: true

module Mutations
  class Purchases::PriceCreate < BaseMutation
    description "Creates a new price"

    field :price, Types::Objects::Purchases::PriceType, null: false

    argument :price_input, Types::Inputs::Purchases::PriceInputType, required: true

    def resolve(price_input:)
      purchases_price = ::Purchases::Price.new(**price_input)
      raise GraphQL::ExecutionError.new "Error creating price", extensions: purchases_price.errors.to_hash unless purchases_price.save

      { price: purchases_price }
    end
  end
end
