# frozen_string_literal: true

module Mutations
  class Purchases::ProductPurchaseInitiate < BaseMutation
    description "Initiates a new transaction"

    field :transaction, Types::Objects::Purchases::ProductPurchaseType, null: true

    argument :transaction_input, Types::Inputs::Purchases::ProductPurchaseInputType, required: true

    def resolve(transaction_input:)
      MultiTenantSupport::Current.tenant_account = nil
      MultiTenantSupport.turn_off_protection

      tenant =  ::Purchases::Price.find(transaction_input[:price_id]).tenant
      transactions_transaction = nil
      MultiTenantSupport.under_tenant tenant do
        transactions_transaction = ::Purchases::ProductPurchase.new(
          **transaction_input,
          tenant: tenant,
          user: context[:current_user],
          origin: context[:origin]
        )
        raise GraphQL::ExecutionError.new "Error initiating transaction", extensions: transactions_transaction.errors.to_hash unless transactions_transaction.initiate
        { transaction: transactions_transaction }
      end
    rescue => e
      Rails.logger.error "Error: #{e}"
      Rollbar.error(e)
      { transaction: transactions_transaction }
    end
  end
end
