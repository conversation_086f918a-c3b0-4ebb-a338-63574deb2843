module Mutations
  class Purchases::SubscribeToFreePack < BaseMutation
    # TODO: define return fields
    field :new_subscriptions, [Types::Objects::Subscriptions::SubscriptionType], null: false

    argument :pack_id, Integer, required: true
    argument :price_id, Integer, required: false
    argument :coupon_code, String, required: false

    def resolve(pack_id:, price_id: nil, coupon_code: nil)
      student = context[:current_user]
      pack = ::Courses::Pack.visible.find(pack_id)
      full_discount = false

      unless pack.is_free # this is to verify coupon applied 100 % is valid or not
        coupon = ::Purchases::Coupon.find_by(code: coupon_code)
        purchases_price = ::Purchases::Price.find_by(id: price_id)
        raise GraphQL::ExecutionError, "This course is not free" if coupon.nil? || coupon.status != 'active' || !(coupon.products.include? purchases_price.product)

        full_discount = coupon&.discount_percent&.to_i == 100
      end
      raise GraphQL::ExecutionError, "This pack is not free" unless pack.is_free || full_discount

      new_subscriptions = pack.add_student!(student, "free_pack_select", student)
      super_admin = nil
      without_tenant_protection do
        super_admin = context[:current_tenant].super_admin
      end
      UserMessagesManager.send_message_to_user(super_admin, :admin_new_free_product, template_variables: { product_name: pack.name, student_name: student.first_name },
                                                                                                              extras: { avatar_data: student.avatar_url, student_id: student.id })

      ::Triggers::Trigger.apply_triggers('admin_new_free_product', student)
      {
        new_subscriptions: new_subscriptions
      }
    end
  end
end
