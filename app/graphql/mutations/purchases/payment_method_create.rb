# frozen_string_literal: true

module Mutations
  class Purchases::PaymentMethodCreate < BaseMutation
    description "Creates a new payment_method"

    field :payment_method, Types::Interfaces::Purchases::PaymentMethodType, null: false

    argument :payment_method_input, Types::Inputs::Purchases::PaymentMethodInputType, required: true

    def resolve(payment_method_input:)

      return { payment_method: '' } if context[:current_tenant].feature_plan_id == 'free'

      # Extract config from the input
      payment_method_hash = payment_method_input.to_h
      config = payment_method_hash.delete(:config).deep_transform_keys! { |key| key.underscore }
      #type = payment_method_hash.delete(:payment_method_type)
      
      #pp type
      
      purchases_payment_method = ::Purchases::PaymentMethod.new(**payment_method_hash, **config)
      raise GraphQL::ExecutionError.new "Error creating payment_method", extensions: purchases_payment_method.errors.to_hash unless purchases_payment_method.save

      { payment_method: purchases_payment_method }
    end
  end
end
