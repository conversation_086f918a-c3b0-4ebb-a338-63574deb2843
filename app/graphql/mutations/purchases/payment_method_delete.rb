# frozen_string_literal: true

module Mutations
  class Purchases::PaymentMethodDelete < BaseMutation
    description "Deletes a payment_method by ID"

    field :payment_method, Types::Interfaces::Purchases::PaymentMethodType, null: false

    argument :id, ID, required: true

    def resolve(id:)
      purchases_payment_method = ::Purchases::PaymentMethod.find(id)
      raise GraphQL::ExecutionError.new "Error deleting payment_method", extensions: purchases_payment_method.errors.to_hash unless purchases_payment_method.destroy

      { payment_method: purchases_payment_method }
    end
  end
end
