# frozen_string_literal: true

module Mutations
  class Purchases::PaymentMethodUpdate < BaseMutation
    description "Updates a payment_method by id"

    field :payment_method, Types::Interfaces::Purchases::PaymentMethodType, null: false

    argument :id, ID, required: true
    argument :payment_method_input, Types::Inputs::Purchases::PaymentMethodInputType, required: true

    def resolve(id:, payment_method_input:)
      purchases_payment_method = ::Purchases::PaymentMethod.find(id)

      payment_method_hash = payment_method_input.to_h

      config = payment_method_hash.delete(:config).deep_transform_keys! { |key| key.underscore }

      raise GraphQL::ExecutionError.new "Error updating payment_method", extensions: purchases_payment_method.errors.to_hash unless purchases_payment_method.update(**payment_method_hash, **config)
      { payment_method: purchases_payment_method }
    end
  end
end
