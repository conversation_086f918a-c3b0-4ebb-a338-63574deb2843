module Mutations
  class Websites::UpdatePersonalTestimonial < BaseMutation
    field :personalTestimonial, Types::Objects::Websites::PersonalTestimonialType, null: false

    argument :personalTestimonialInput, Types::Inputs::Websites::PersonalTestimonialInputType, required: true
    argument :id, Integer, "The ID of the personal testimonial component to be updated", required: true

    def resolve(id:, personalTestimonialInput:)
      personalTestimonial = ::Websites::PersonalTestimonial.find(id)
      personalTestimonial.update!(**personalTestimonialInput)
      { personalTestimonial: personalTestimonial }
    end
  end
end