module Mutations
  class Websites::DeleteFrequentQuestion < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    argument :id, ID, "The ID of the question to be deleted", required: true

    def resolve(id:)
      frequent_question = ::Websites::BodySectionFrequentQuestion.find(id)
      if frequent_question.destroy
        { success: true }
      else
        { success: false }
      end
    end
  end
end
