module Mutations
  class Websites::UpdateLeadsFormViews < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    argument :email_lead_form_id, ID, required: true

    def resolve(email_lead_form_id:)
      without_tenant_protection do
        email_lead_form = ::Websites::BodySectionEmailLeadsForm.find_by(id: email_lead_form_id)
        if email_lead_form&.increment!(:views_count, 1)
          { success: true }
        else
          { success: false }
        end
      end
    end
  end
end
