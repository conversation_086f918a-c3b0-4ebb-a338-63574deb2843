module Mutations
  class Websites::DeletePriceCardItem < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :id, ID, required: true

    def resolve(id:)
      price_card_item = fetch_price_section_card_item(id)
      return { success: false, errors: ['Price Card Item Not Found'] } unless price_card_item.present?

      if price_card_item.destroy
        { success: true, errors: [] }
      else
        { success: false, errors: price_card_item.errors.messages }
      end
    end
  end
end
