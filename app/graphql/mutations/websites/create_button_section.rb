module Mutations
  class Websites::CreateButtonSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionButtonType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionButtonInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionButton.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
