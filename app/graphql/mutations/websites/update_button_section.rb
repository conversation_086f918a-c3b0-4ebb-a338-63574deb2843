module Mutations
  class Websites::UpdateButtonSection < ::Mutations::BaseMutation
    field :section, Types::Objects::Websites::BodySectionButtonType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionButtonInputType, required: true
    argument :id, Integer, "The ID of the body section component to be updated", required: true

    def resolve(id:, section_input:)
      section = fetch_website_section_content(id, ::Websites::BodySectionButton)
      section.update!(**section_input)
      { section: section }
    end
  end
end
