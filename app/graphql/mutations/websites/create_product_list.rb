module Mutations
  class Websites::CreateProductList < BaseMutation
    field :section, Types::Objects::Websites::BodySectionProductListType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionProductListInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionProductList.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
