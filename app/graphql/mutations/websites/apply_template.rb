module Mutations
  class Websites::ApplyTemplate < ::Mutations::BaseMutation
    field :page, Types::Objects::WebsiteType, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :template_input, Types::Inputs::Websites::ApplyTemplatesInputType, required: true

    def resolve(template_input:)
      template_id = template_input.template_id
      website_id = template_input.website_id
      template = Template.find_by(id: template_id)
      page = nil
      ActiveRecord::Base.transaction do
        page = if template_input.create_new_page
          create_new_page(template_input)
        else
          context[:current_tenant].websites.find_by(id: website_id)
        end
        template_page = nil
        page.body_sections.destroy_all if template_input.replace
        MultiTenantSupport.without_current_tenant do
          MultiTenantSupport.turn_off_protection do
            template_page = template.website
            new_tenant = template_page.tenant
            if page.duplicate_sections(template_page)
              { success:  true, page: page }
            else
              { success:  false, errors: page.errors.full_messages, page: page }
            end
          end
        end
      end
    rescue => e
      { success:  false, errors: [e.message], page: page || Website.new}
    end

    def create_new_page(template_input)
      page_title = template_input.page_title
      page_url = "/#{page_title.gsub(/\s+/, '-').downcase}"
      Website.create!(title: page_title, url: page_url)
    end
  end
end
