module Mutations
    class Websites::UpdateButtonStyle < BaseMutation
      field :button, Types::Objects::Websites::StyleType, null: false
  
      argument :buttonInput, Types::Inputs::Websites::StyleInputType, required: true
      argument :id, Integer, "The ID of the button style to be updated", required: true
  
      def resolve(id:, buttonInput:)
        button = fetch_website_btn_style(id)
        button.update!(**buttonInput)
        { button: button }
      end
    end
end