module Mutations
  module Websites
    class ReorderFrequentQuestionSection < ::Mutations::BaseMutation
      field :success, <PERSON><PERSON><PERSON>, null: false

      argument :id, ID, "The ID of the question to be repositioned", required: true
      argument :position, Integer, "The new position of the question", required: true

      def resolve(id:, position:)
        item = ::Websites::BodySectionFrequentQuestion.find(id)
        item.insert_at(position)
        { success: true }
      end
    end
  end
end
