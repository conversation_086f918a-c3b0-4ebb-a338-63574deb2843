module Mutations
  class Websites::UpdateWebsite < ::Mutations::BaseMutation
    field :page, Types::Objects::WebsiteType, null: false
    argument :id, ID, required: true 
    argument :page_input, Types::Inputs::Websites::WebsiteInputType, required: true

    def resolve(page_input:, id:)
      footer_input = page_input.delete(:footer)
      page = fetch_website(id)

      page.update!(**page_input)

      # Update footer if provided and this is a homepage
      if footer_input.present? && page.homepage?
        footer = page.footer || page.build_footer(tenant: context[:current_tenant])
        footer.update!(**footer_input)
      end

      { page: page }
    end
  end
end
