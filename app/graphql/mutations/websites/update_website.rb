module Mutations
  class Websites::UpdateWebsite < ::Mutations::BaseMutation
    field :page, Types::Objects::WebsiteType, null: false
    argument :id, ID, required: true 
    argument :page_input, Types::Inputs::Websites::WebsiteInputType, required: true

    def resolve(page_input:, id:)
      footer_input = page_input.delete(:footer)
      page = fetch_website(id)

      page.update!(**page_input)

      # Update footer if provided and this is a homepage
      if footer_input.present? && page.homepage?
        footer_columns_input = footer_input.delete(:footer_columns)
        footer = page.footer || page.build_footer(tenant: context[:current_tenant])
        footer.update!(**footer_input)

        # Update footer columns if provided
        if footer_columns_input.present?
          # Remove existing columns
          footer.footer_columns.destroy_all

          # Create new columns
          footer_columns_input.each_with_index do |column_input, index|
            footer.footer_columns.create!(
              tenant: context[:current_tenant],
              position: index + 1,
              **column_input
            )
          end
        end
      end

      { page: page }
    end
  end
end
