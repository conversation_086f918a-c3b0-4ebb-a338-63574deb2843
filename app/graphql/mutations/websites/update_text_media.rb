module Mutations
  class Websites::UpdateTextMedia < ::Mutations::BaseMutation
    field :section, Types::Objects::Websites::BodySectionTextMediaType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionTextMediaInputType, required: true
    argument :id, Integer, "The ID of the body section component to be updated", required: true

    def resolve(id:, section_input:)
      section = fetch_website_section_content(id, ::Websites::BodySectionTextMedia)
      section.update!(**section_input)
      { section: section }
    end
  end
end
