module Mutations
  class Websites::CreateLeadsFormContact < BaseMutation
    field :lead_form_contact, Types::Objects::Websites::LeadsFormContactType, null: false
    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true

    argument :contact_input, Types::Inputs::Websites::LeadsFormContactInputType, required: true
    argument :email_lead_form_id, ID, required: true

    def resolve(contact_input:, email_lead_form_id:)
      email_lead_form = fetch_website_section_content(email_lead_form_id, ::Websites::BodySectionEmailLeadsForm)
      lead_form_contact = email_lead_form.lead_form_contacts.new(**contact_input)

      if lead_form_contact.save
        { lead_form_contact: lead_form_contact, success: true }
      else
        { errors: lead_form_contact.errors.full_messages, success: false, lead_form_contact: lead_form_contact }
      end
    end
  end
end
