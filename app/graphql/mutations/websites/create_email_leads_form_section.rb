module Mutations
  class Websites::CreateEmailLeadsFormSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionEmailLeadsFormType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionEmailLeadsFormInputType, required: true
    argument :website_id, ID, required: true
    argument :disable_section, <PERSON><PERSON><PERSON>, required: false

    def resolve(section_input:, website_id:, disable_section: nil)
      validate_website_access(website_id)

      if disable_section.present?
        section = ::Websites::BodySectionEmailLeadsForm.create!(**section_input)
      else
        section = ::Websites::BodySectionEmailLeadsForm.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      end
      {
        section: section
      }
    end
  end
end
