module Mutations
  class Websites::DeleteWebsite < ::Mutations::BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :id, ID, required: true

    def resolve(id:)
      page = fetch_website(id)
      return { success:false, errors: ['Page Not Found'] } unless page.present?

      if page.destroy
        { success: true, errors: [] }
      else
        { success: false, errors: page.errors.messages }
      end
    end
  end
end
