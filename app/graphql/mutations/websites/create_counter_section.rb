module Mutations
  class Websites::CreateCounterSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionCounterType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionCounterInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionCounter.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
