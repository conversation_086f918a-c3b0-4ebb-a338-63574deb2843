module Mutations
  class Websites::UpdateButton < BaseMutation
    field :button, Types::Objects::Websites::ButtonType, null: false

    argument :buttonInput, Types::Inputs::Websites::ButtonInputType, required: true
    argument :id, Integer, "The ID of the button component to be updated", required: true

    def resolve(id:, buttonInput:)
      button = fetch_website_button(id)
      button.update!(**buttonInput)
      { button: button }
    end
  end
end