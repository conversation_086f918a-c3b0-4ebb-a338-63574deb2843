module Mutations
  class Websites::CreatePersonalTestimonial < BaseMutation
    field :personalTestimonial, Types::Objects::Websites::PersonalTestimonialType, null: false

    argument :personalTestimonialInput, Types::Inputs::Websites::PersonalTestimonialInputType, required: true

    def resolve(personalTestimonialInput:)
      value = ::Websites::PersonalTestimonial.create!( **personalTestimonialInput)
      {
        personalTestimonial: value
      }
    end
  end
end
