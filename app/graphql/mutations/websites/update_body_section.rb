module Mutations
  module Websites
    class UpdateBodySection < ::Mutations::BaseMutation
      field :section, Types::Objects::Websites::BodySectionType, null: false

      argument :id, Integer, "The ID of the body section", required: true
      argument :section_input, Types::Inputs::Websites::BodySectionInputType, required: true

      def resolve(id:, section_input:)
        section = fetch_website_section(id)
        section.update(**section_input)
        { section: section }
      end
    end
  end
end
