module Mutations
  class Websites::DeletePriceCard < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :id, ID, required: true

    def resolve(id:)
      price_card = fetch_price_section_card(id)
      return { success: false, errors: ['Price Card Not Found'] } unless price_card.present?

      if price_card.destroy
        { success: true, errors: [] }
      else
        { success: false, errors: price_card.errors.messages }
      end
    end
  end
end
