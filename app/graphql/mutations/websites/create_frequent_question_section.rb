module Mutations
  class Websites::CreateFrequentQuestionSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionFaqType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionFaqInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionFaq.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
