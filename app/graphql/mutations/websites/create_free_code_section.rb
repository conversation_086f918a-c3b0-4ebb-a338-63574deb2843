module Mutations
  class Websites::Create<PERSON>reeCodeSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionFreeCodeType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionFreeCodeInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionFreeCode.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
