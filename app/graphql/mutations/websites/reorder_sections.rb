module Mutations
  module Websites
    class ReorderSections < ::Mutations::BaseMutation
      description "Places a section at the given position. All other sections will be moved down"
      field :bodySections, [Types::Objects::Websites::BodySectionType], null: false

      argument :id, Integer, "The ID of the section to be repositioned", required: true
      argument :position, Integer, "The new position of the section", required: true

      def resolve(id:, position:)
        item = fetch_website_section(id)
        item.insert_at(position)
        { bodySections: ::Websites::BodySection.where(website_id: item.website_id).order(position: :asc) }
      end
    end
  end
end
