module Mutations
  class Websites::CreateTextMedia < BaseMutation
    field :section, Types::Objects::Websites::BodySectionTextMediaType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionTextMediaInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionTextMedia.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
