module Mutations
  class Websites::UpdateMediaSection < ::Mutations::BaseMutation
    field :section, Types::Objects::Websites::BodySectionMediaType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionMediaInputType, required: true
    argument :id, Integer, "The ID of the body section component to be updated", required: true

    def resolve(id:, section_input:)
      section = fetch_website_section_content(id, ::Websites::BodySectionMedia)
      section.update!(**section_input)
      { section: section }
    end
  end
end
