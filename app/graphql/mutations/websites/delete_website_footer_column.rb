module Mutations
  class Websites::DeleteWebsiteFooterColumn < ::Mutations::BaseMutation
    description "Deletes a website footer column"

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: true

    argument :id, Integer, required: true, description: "ID of the footer column to delete"

    def resolve(id:)
      binding.pry
      footer_column = fetch_website_footer_column(id)
      t = context[:cuurent_tenant]
      MultiTenantSupport.under_tenant t do
        if footer_column.destroy
          { success: true, errors: [] }
        else
          { success: false, errors: footer_column.errors.messages }
        end     
      end
    end
  end
end
