module Mutations
  class Websites::DeletePersonalTestimonial < BaseMutation
    field :personalTestimonial, Types::Objects::Websites::PersonalTestimonialType, null: false
    argument :id, Integer, "The ID of the personal testimonial component to be deleted", required: true

    def resolve(id:)
      personalTestimonial = ::Websites::PersonalTestimonial.find(id)
      personalTestimonial.destroy!
      { personalTestimonial: personalTestimonial }
    end
  end
end