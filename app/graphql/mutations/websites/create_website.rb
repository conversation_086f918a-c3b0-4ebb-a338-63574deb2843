module Mutations
  class Websites::CreateWebsite < ::Mutations::BaseMutation
    field :page, Types::Objects::WebsiteType, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :page_input, Types::Inputs::Websites::WebsiteInputType, required: true

    def resolve(page_input:)
      footer_input = page_input.delete(:footer)

      if context[:current_user].type == 'Affiliate'
        page = context[:current_user].websites.new(**page_input)
      else
        page = context[:current_tenant].websites.new(**page_input)
      end

      if page.save
        # Create footer if provided and this is a homepage
        if footer_input.present? && page.homepage?
          footer_columns_input = footer_input.delete(:footer_columns)

          footer = page.build_footer(
            tenant: context[:current_tenant],
            **footer_input
          )

          unless footer.save
            return { errors: footer.errors.full_messages, success: false, page: page }
          end

          # Create footer columns if provided
          if footer_columns_input.present?
            footer_columns_input.each_with_index do |column_input, index|
              column = footer.footer_columns.build(
                tenant: context[:current_tenant],
                position: index + 1,
                **column_input
              )
              unless column.save
                return { errors: column.errors.full_messages, success: false, page: page }
              end
            end
          end
        end

        { page: page, success: true }
      else
        { errors: page.errors.full_messages, success: false, page: page }
      end
    end
  end
end
