module Mutations
  class Websites::CreateWebsite < ::Mutations::BaseMutation
    field :page, Types::Objects::WebsiteType, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :page_input, Types::Inputs::Websites::WebsiteInputType, required: true

    def resolve(page_input:)
      if context[:current_user].type == 'Affiliate'
        page = context[:current_user].websites.new(**page_input)
      else
        page = context[:current_tenant].websites.new(**page_input)
      end
      if page.save
        { page: page, success: true }
      else
        { errors: page.errors.full_messages, success: false, page: page }
      end
    end
  end
end
