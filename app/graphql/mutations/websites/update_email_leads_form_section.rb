module Mutations
  class Websites::UpdateEmailLeadsFormSection < ::Mutations::BaseMutation
    field :section, Types::Objects::Websites::BodySectionEmailLeadsFormType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionEmailLeadsFormInputType, required: true
    argument :id, Integer, "The ID of the body section component to be updated", required: true

    def resolve(id:, section_input:)
      section = fetch_website_section_content(id, ::Websites::BodySectionEmailLeadsForm)
      section.update!(**section_input)
      { section: section }
    end
  end
end
