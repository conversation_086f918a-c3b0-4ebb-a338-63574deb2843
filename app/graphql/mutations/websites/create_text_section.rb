module Mutations
  class Websites::CreateTextSection < BaseMutation
    field :section, Types::Objects::Websites::BodySectionTextType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionTextInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      {
        section: ::Websites::BodySectionText.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      }
    end
  end
end
