module Mutations
  module Websites
    class DeleteBodySection < ::Mutations::BaseMutation
     
      field :section, Types::Objects::Websites::BodySectionType, null: false
      argument :id, Integer, "The ID of the body section component to be removed", required: true
      def resolve(id:)
        section = fetch_website_section(id)
        section.destroy!
        { section: section }
      end
    end
  end
end
