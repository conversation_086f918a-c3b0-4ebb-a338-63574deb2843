module Mutations
  class Websites::CreateTestimonial < BaseMutation
    field :section, Types::Objects::Websites::BodySectionTestimonialType, null: false

    argument :section_input, Types::Inputs::Websites::BodySectionTestimonialInputType, required: true
    argument :website_id, ID, required: true

    def resolve(section_input:, website_id:)
      validate_website_access(website_id)
      value = ::Websites::BodySectionTestimonial.create!(body_section: ::Websites::BodySection.new(website_id:), **section_input)
      {
        section: value
      }
    end
  end
end
