module Mutations
  class BuyPlanSubscription < BaseMutation
    field :checkout_url, String, null: false

    argument :price_id, String, required: true
    argument :gl_param, String, required: false

    def resolve(price_id:, gl_param: nil)
      Stripe.api_key = Rails.application.credentials.stripe.api_secret
      tenant = context[:current_tenant]
      customer_id = tenant.stripe_customer_id
      incomplete_subscription = nil
      success_query_params = "session_id={CHECKOUT_SESSION_ID}"
      cancel_query_params = ""
      session_url = ""

      if tenant&.tenant_metric&.utm_gl_data.present? || gl_param.present?
        gl_value = tenant&.tenant_metric&.utm_gl_data || gl_param
        success_query_params += "&_gl=#{CGI.escape(gl_value)}"
        cancel_query_params = "?_gl=#{CGI.escape(gl_value)}"
      end

      if customer_id.present?
        incomplete_subscription = Stripe::Subscription.list(customer: customer_id, status: 'incomplete', limit: 5, plan: price_id).first
      end

      if incomplete_subscription.present?
        begin
          invoice_id = incomplete_subscription.latest_invoice
          invoice = Stripe::Invoice.retrieve(invoice_id)
          session_url = invoice.hosted_invoice_url
        rescue => e
          Rails.logger.error("Stripe invoice retrieval failed: #{e.message}")
        end
      end

      unless session_url.present?
        session_args = {
          client_reference_id: tenant.id,
          success_url: "#{tenant.academy_url}/callbacks/stripe_subscription_success?#{success_query_params}",
          cancel_url: "#{tenant.academy_url}/callbacks/stripe_subscription_cancel#{cancel_query_params}",
          mode: 'subscription',
          locale: tenant.language == 'pt-br' ? 'pt-BR' : tenant.language,
          line_items: [{
            quantity: 1,
            price: price_id
          }]
        }
        if tenant.stripe_customer_id.present?
          session_args[:customer] = tenant.stripe_customer_id
        else
          without_tenant_protection do
            session_args[:customer_email] = tenant.super_admin.email
          end
        end
        session_url = Stripe::Checkout::Session.create(**session_args).url
      end
      { checkout_url: session_url }
    end
  end
end
