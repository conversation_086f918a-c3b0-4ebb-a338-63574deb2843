module Mutations
  class DeleteWebsiteFooterColumn < BaseMutation
    description "Deletes a website footer column"

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: true

    argument :id, Integer, required: true, description: "ID of the footer column to delete"

    def resolve(id:)
      footer_column = context[:current_tenant].website_footer_columns.find(id)
      
      if footer_column.destroy
        { success: true, footer_column: footer_column }
      else
        raise GraphQL::ExecutionError.new "Error deleting footer column", extensions: footer_column.errors.to_hash
      end
    end
  end
end
