module Mutations
  class CreateNewTenant < BaseMutation
    include UrlShortener

    field :success, Boolean, null: false
    field :tenant, Types::Objects::TenantType, null: true
    field :errors, [String], null: false

    argument :tenant_input, Types::Inputs::CreateTenantInputType, required: true

    def resolve(tenant_input:)
      ActiveRecord::Base.transaction do
        tenant = Tenant.create!(ip_address: context[:ip_address], **tenant_input)
        context[:current_user].users_tenants.create(tenant_id: tenant.id)
        MultiTenantSupport.under_tenant tenant do
          super_admin = SuperAdmin.create!(context[:current_user].attributes.slice('email', 'first_name', 'last_name', 'password','uid', 'bio','country','language','timezone', 'phone_number'))
          super_admin.update(encrypted_password: context[:current_user].encrypted_password)
          tenant.create_models
        end
        { success: true, tenant: tenant, errors: [] }
      end
    rescue => e
      { success: false, errors: [e.message] }
    end
  end
end
