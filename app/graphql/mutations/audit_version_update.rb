module Mutations
    class AuditVersionUpdate < BaseMutation
      field :version, Types::Objects::VersionType, null: true
      field :errors, [String], null: false 

      argument :id, ID, required: true
      argument :version_input, Types::Inputs::VersionInputType, required: true
      def resolve(id:, version_input:)
        version = AuditVersion.find(id)
        
        if version.update(**version_input)
          {
            version: version,
            errors: []
          }
        else
          {
            version: nil,
            errors: version.errors.full_messages
          }
        end
      end
    end
  end
  