module Mutations
  class Affiliates::CreateAffiliateLink < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :course_id, ID, required: true
    argument :affiliate_id, ID, required: true

    def resolve(affiliate_id:, course_id:)
      # Here current_user will be Student
      if context[:current_user].type == 'Student'
        without_tenant_protection do
          link = context[:current_user].affiliates_links.find_or_create_by!(course_id:, affiliate_id:)
        end
        { success: true }
      else
        { success: false }
      end
    rescue => e
      { success: false }
    end
  end
end
