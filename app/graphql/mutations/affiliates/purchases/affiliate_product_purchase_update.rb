module Mutations
  class Affiliates::Purchases::AffiliateProductPurchaseUpdate < BaseMutation
    description "Update a product_purchase by id"

    field :product_purchase, Types::Objects::Purchases::ProductPurchaseType, null: false

    argument :id, ID, required: true
    argument :product_purchase_input, Types::Inputs::Purchases::ProductPurchaseInputType, required: true

    def resolve(id:, product_purchase_input:)
      purchases_product_purchase = context[:current_user].product_purchases.find(id)

      raise GraphQL::ExecutionError.new "Error updating product_purchase", extensions: purchases_product_purchase.errors.to_hash unless purchases_product_purchase.update(**product_purchase_input)
      { product_purchase: purchases_product_purchase }
    end
  end
end
