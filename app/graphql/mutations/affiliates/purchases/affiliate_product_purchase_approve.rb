# frozen_string_literal: true
module Mutations
  class Affiliates::Purchases::AffiliateProductPurchaseApprove < BaseMutation

    field :product_purchase, Types::Objects::Purchases::ProductPurchaseType, null: false

    argument :id, ID, required: true

    def resolve(id:)
      purchases_product_purchase = context[:current_user].product_purchases.find(id)
      tenant = purchases_product_purchase.tenant
      under_tenant_context(tenant) do
        purchases_product_purchase.approve! unless purchases_product_purchase.approved?
          
        { product_purchase: purchases_product_purchase }
      end
    rescue ActiveRecord::RecordNotFound => e
      raise GraphQL::ExecutionError.new "product_purchase not exist", extensions: {error: e.message}
    end
  end
end
