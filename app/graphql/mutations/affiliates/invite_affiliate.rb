module Mutations
  class Affiliates::InviteAffiliate < BaseMutation
    field :success, <PERSON><PERSON>an, null: false
    field :errors, [String], null: true

    argument :email, String, required: true

    def resolve(email:)
      # Create an affiliate invitation
      without_tenant_protection do
        invitation = AffiliateInvitation.find_or_initialize_by(email:)
        invitation.token = SecureRandom.hex(10)
        invitation.inviter_id = context[:current_user].id if invitation.new_record?

        if invitation.save
          AffiliateMailer.with(invitation: invitation, origin: context[:origin], tenant: context[:current_tenant]).invite_affiliate.deliver_now

          { success: true, errors: [] }
        else
          { success: false, errors: invitation.errors.full_messages }
        end
      end
    end
  end
end
