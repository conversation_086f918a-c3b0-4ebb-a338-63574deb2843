module Mutations
  class Affiliates::ConfirmAffiliate < BaseMutation
    argument :confirmation_token, String, required: true

    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: false

    def resolve(confirmation_token:)
      without_tenant_protection do

        users = User.where(affiliate_confirmation_token: confirmation_token, type: ['SuperAdmin', 'Student'])

        unless users.present?
          return { success: false, errors: ["Invalid or expired token"] }
        end

        user = users.first
 
        affiliate_data = {
          email: user.email,
          password: user.password,
          country: user.country,
          ip_address: user.ip_address,
          first_name: user.first_name,
          last_name: user.last_name,
          affiliate_confirmed_at: user.affiliate_confirmed_at,
          affiliate_user: true,
          provider: user.provider
        }
        affiliate_user = Affiliate.new(affiliate_data)

        if affiliate_user.save
          users.update_all(affiliate_id: affiliate_user.id, affiliate_confirmation_token: nil, affiliate_confirmed_at: Time.current, affiliate_user: true)

          { success: true, errors: [] }
        else
          { success: false, errors: affiliate_user.errors.full_messages }
        end
      end
    end
  end
end
