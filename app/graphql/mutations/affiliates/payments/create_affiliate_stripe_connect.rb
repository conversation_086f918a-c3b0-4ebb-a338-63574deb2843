module Mutations
  class Affiliates::Payments::CreateAffiliateStripeConnect < BaseMutation
    field :checkout_url, String, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    def resolve
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
      account_data = {
        type: 'express',
        settings: {
          payouts: {
            schedule: {
              interval: 'daily',
              delay_days: 30
            },
          }
        }
      }
      account = Stripe::Account.create(account_data)

      payment_method = context[:current_user].payment_methods.find_or_initialize_by(name: 'Sabionet Payments', type: 'Affiliates::PaymentMethodStripeConnect')
      payment_method.account_id = account.id
      payment_method.save!

      account_link = Stripe::AccountLink.create({
        account: account.id,
        refresh_url: "#{context[:origin]}/affiliate/sabioPayments",
        return_url: "#{context[:origin]}/affiliate/sabioPayments",
        type: 'account_onboarding',
      })

      { checkout_url: account_link.url, success: :true, errors: [] }
    rescue => e
      { checkout_url: '', success: false, errors: [e]}
    end
  end
end
