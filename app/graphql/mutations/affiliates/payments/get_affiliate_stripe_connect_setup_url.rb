module Mutations
  class Affiliates::Payments::GetAffiliateStripeConnectSetupUrl < BaseMutation
    field :setup_url, String, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    def resolve
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
      payment_method = context[:current_user].payment_methods.find_by(name: 'Sabionet Payments')
      account_link = Stripe::AccountLink.create({
        account: payment_method.account_id,
        refresh_url: "#{context[:origin]}/affiliate/sabioPayments",
        return_url: "#{context[:origin]}/affiliate/sabioPayments",
        type: 'account_onboarding',
      })

      { setup_url: account_link.url, success: :true, errors: [] }
    rescue => e
      { setup_url: '', success: false, errors: [e]}
    end
  end
end
