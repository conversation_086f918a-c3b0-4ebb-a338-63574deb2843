module Mutations
  class Affiliates::Refunds::UpdateAffiliateRefundRequest < ::Mutations::BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :refund_request_input, Types::Inputs::Refunds::RefundRequestInputType, required: true

    def resolve(refund_request_input:)
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

      status = refund_request_input[:status]
      refund_request_id = refund_request_input[:refund_request_id]
      refund_request = context[:current_user].refund_requests.find(refund_request_id)
      under_tenant_context(refund_request.tenant) do
        if status == 'approved'
          response = refund_request.product_purchase.process_refund
          success = response.status == 'succeeded'
          refund_request.approved! if success
          { success: }
        elsif status == 'rejected'
          refund_request.rejected!
          { success: true }
        end
      end
    rescue =>  e
      { success: false }
    end
  end
end
