module Mutations
  class Affiliates::AffiliateCourseRequest < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :course_id, ID, required: true
    field :errors, [String], null: true

    def resolve(course_id:)
      course = ::Courses::Course.find(course_id)
      if course.present? && course.is_available_on_affiliate
        if context[:current_user].affiliates_courses.find_by(course_id:, status: :approved).present?
          return { success: false, errors: ["Ya afiliada"] }
        elsif context[:current_user].affiliates_courses.find_by(course_id:, status: :pending).present?
          return { success: false, errors: ["La solicitud está pendiente"] }
        end
        request = context[:current_user].affiliates_courses.new(course_id:)
        unless course.affiliate_setting.approval_requirement
          request.status = :approved
        end
        { success: request.save }
      end
    rescue => e
      { success: false }
    end
  end
end
