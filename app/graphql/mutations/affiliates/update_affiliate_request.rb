module Mutations
  class Affiliates::UpdateAffiliateRequest < BaseMutation
    field :success, Boolean, null: false
    field :affiliate_request, Types::Objects::Affiliates::AffiliateRequestType, null: false

    argument :id, ID, required: true
    argument :affiliate_request_input, Types::Inputs::Affiliates::AffiliateRequestInputType, required: true

    def resolve(id:, affiliate_request_input:)
      affiliate_request = ::AffiliatesCourse.find(id)
      without_tenant_protection do
        if affiliate_request.update(affiliate_request_input.to_h)
          { success: true, affiliate_request: affiliate_request  }
        else
          { success: false, affiliate_request: affiliate_request  }
        end
      end
    end
  end
end
