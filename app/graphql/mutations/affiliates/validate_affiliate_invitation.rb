module Mutations
  class Affiliates::ValidateAffiliateInvitation < BaseMutation
    argument :token, String, required: true
    argument :email, String, required: false

    field :valid, <PERSON><PERSON>an, null: false
    field :error, String, null: true
    field :invitation_details, Types::Objects::Affiliates::AffiliateInvitationType, null: true

    def resolve(token:, email: nil)
      invitation = AffiliateInvitation.find_by(token: token)

      if invitation.nil?
        { valid: false, error: 'Invalid invitation token.', invitation_details: nil }
      elsif invitation.expired?
        { valid: false, error: 'The invitation has expired.', invitation_details: nil }
      elsif email.present? && invitation.email != email
        { valid: false, error: 'Email does not match the invitation.', invitation_details: nil }
      else
        { valid: true, error: nil, invitation_details: invitation }
      end
    end
  end
end
