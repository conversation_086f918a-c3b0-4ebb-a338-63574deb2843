module Mutations
  class Affiliates::SendAffiliateConfirmationEmail < BaseMutation
    argument :email, String, required: true

    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: false

    def resolve(email:)
      without_tenant_protection do
        users = User.where(email: email, type: ['SuperAdmin', 'Student'])

        return { success: false, errors: ["User not found"] } unless users.present?
        # return { success: false, errors: ["User is not a SuperAdmin"] } unless user.super_admin?
        user = users.first
        confirmation_token = SecureRandom.hex(16)
        users.update_all(affiliate_confirmation_token: confirmation_token)

        AffiliateMailer.send_confirmation_email(user.id, confirmation_token).deliver_later

        { success: true, errors: [] }
      end
    rescue => e
      { success: false, errors: [e.message] }
    end
  end
end
