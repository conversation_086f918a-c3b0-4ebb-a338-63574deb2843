module Mutations::Affiliates
  class LoginAsAffiliate < Mutations::BaseMutation
    argument :email, String, required: true

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true
    field :headers, GraphQL::Types::JSON, null: true

    def resolve(email:)
      affiliate = Affiliate.find_by("LOWER(email) = ?", email.downcase)

      unless affiliate.present? && context[:current_user]&.email&.strip&.downcase == email.strip.downcase && context[:current_user].affiliate_user
        return { success: false, errors: ["Affiliate user not found"], headers: {} }
      end

      login_token = affiliate.create_new_auth_token

      { success: true, headers: login_token, errors: [] }
    rescue => e
      { success: false, errors: [e.message] }
    end
  end
end
