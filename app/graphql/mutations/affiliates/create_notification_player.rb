module Mutations
  class Affiliates::CreateNotificationPlayer < BaseMutation
    argument :player_id, String, required: true
    argument :device_type, String, required: true

    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: false

    def resolve(player_id:, device_type: nil)
      notification_player = context[:current_user].notification_players.find_or_initialize_by(player_id: player_id)
      notification_player.device_type = device_type
      if notification_player.save
        { success: true, errors: [] }
      else
        { success: false, errors: notification_player.errors.full_messages }
      end
    end
  end
end
