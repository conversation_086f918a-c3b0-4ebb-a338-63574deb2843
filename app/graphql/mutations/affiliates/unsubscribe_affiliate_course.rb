module Mutations
  class Affiliates::UnsubscribeAffiliateCourse < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :course_id, ID, required: true

    def resolve(course_id:)
      course = ::Courses::Course.find(course_id)
      if course.present?
        request = context[:current_user].affiliates_courses.find_by(course_id:)
        { success: request.disaffiliate! }
      end
    rescue => e
      { success: false }
    end
  end
end
