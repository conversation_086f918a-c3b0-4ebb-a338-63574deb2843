module Mutations
  class Affiliates::UpdateAffiliate < BaseMutation
    field :success, Boolean, null: false
    field :affiliate, Types::Objects::Affiliates::AffiliateType, null: false

    argument :id, ID, required: true
    argument :affiliate_input, Types::Inputs::Affiliates::AffiliateInputType, required: true

    def resolve(id:, affiliate_input:)
      without_tenant_protection do
        affiliate = ::Affiliate.find(id)
        affiliates_tenants = affiliate.affiliates_tenants.where(tenant_id: context[:current_tenant].id)
        if affiliates_tenants.update_all(status: affiliate_input.to_h[:status]) # affiliate.update(affiliate_input.to_h)
          { success: true, affiliate: affiliate  }
        else
          { success: false, affiliate: affiliate  }
        end
      end
    end
  end
end
