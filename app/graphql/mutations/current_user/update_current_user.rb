module Mutations
  module CurrentUser
    class UpdateCurrentUser < ::Mutations::BaseMutation
      field :current_user, Types::Interfaces::UserType, null: false

      argument :current_user_input, Types::Inputs::CurrentUserInputType, "The user data to be updated", required: true
      argument :is_new_superadmin, <PERSON><PERSON><PERSON>, "If this user is a newly created superadmin", required: false

      def resolve(current_user_input:, is_new_superadmin: false)
        user = context[:current_user]
        MultiTenantSupport.without_current_tenant do
          context[:current_user].update!(**current_user_input)
        end
        user.send_new_superadmin_email if is_new_superadmin && user.is_a?(SuperAdmin)

        { current_user: context[:current_user] }
      end
    end
  end
end
