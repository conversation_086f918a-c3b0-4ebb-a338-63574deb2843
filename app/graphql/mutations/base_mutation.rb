module Mutations
  class BaseMutation < GraphQL::Schema::RelayClassicMutation
    argument_class Types::BaseArgument
    field_class Types::BaseField
    input_object_class Types::BaseInputObject
    object_class Types::BaseObject

    include MultiTenantHelper
    include WebsiteAuthorizable

    def self.visible?(context)
      return true if context.schema == SbrPublicApiSchema
      Pundit.policy!(context[:current_user], :application).public_send("#{@default_graphql_name.underscore}?")
    end
  end
end
