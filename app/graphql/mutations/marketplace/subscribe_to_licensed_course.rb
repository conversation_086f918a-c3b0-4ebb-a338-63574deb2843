module Mutations
  class Marketplace::SubscribeToLicensedCourse < BaseMutation
    field :new_subscription, Types::Objects::Subscriptions::SubscriptionType, null: false

    argument :course_id, Integer, required: true

    def resolve(course_id:)
      student = context[:current_user]
      course = ::Courses::Course.visible.find(course_id)
      raise GraphQL::ExecutionError, "This course is not licensed" unless course.is_licensed

      license = course.licenses.last

      new_subscription = course.add_student!(student, "marketplace", student, license_id: license.id)
      {
        new_subscription: new_subscription
      }
    end
  end
end
