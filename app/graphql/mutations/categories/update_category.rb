module Mutations
  module Categories
    class UpdateCategory < ::Mutations::BaseMutation
      field :category, Types::Objects::Courses::CategoryType, null: false

      argument :id, Integer, "The ID of the module to be updated", required: true
      argument :category_input, Types::Inputs::Categories::CategoryInputType, "The category data to be updated", required: true

      def resolve(id:, category_input:)
        category = ::Courses::Category.find(id)
        category.update!(**category_input)
        { category: category }
      end
    end
  end
end
