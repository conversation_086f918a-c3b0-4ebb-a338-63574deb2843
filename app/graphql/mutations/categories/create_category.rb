module Mutations
  class Categories::CreateCategory < BaseMutation
    description "Creates a new category"

    field :category, Types::Objects::Courses::CategoryType, null: false

    argument :category_input, Types::Inputs::Categories::CategoryInputType, required: true

    
    def resolve(category_input:)
      category = ::Courses::Category.new(**category_input)
      category.save!
      {
        category: category
      }
    end
  end
end
