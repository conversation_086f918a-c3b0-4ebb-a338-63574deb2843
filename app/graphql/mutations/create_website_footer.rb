module Mutations
  class CreateWebsiteFooter < BaseMutation
    description "Creates a website footer for homepage"

    field :website_footer, Types::Objects::WebsiteFooterType, null: false

    argument :website_id, Integer, required: true, description: "ID of the homepage website"
    argument :footer_input, Types::Inputs::WebsiteFooterInputType, required: true

    def resolve(website_id:, footer_input:)
      website = context[:current_tenant].websites.find(website_id)
      
      unless website.homepage?
        raise GraphQL::ExecutionError, "Footer can only be created for homepage websites"
      end
      
      if website.footer.present?
        raise GraphQL::ExecutionError, "Footer already exists for this website"
      end

      website_footer = website.build_footer(
        tenant: context[:current_tenant],
        **footer_input
      )
      
      raise GraphQL::ExecutionError.new "Error creating website footer", extensions: website_footer.errors.to_hash unless website_footer.save

      { website_footer: website_footer }
    end
  end
end
