module Mutations::Users
  class UpdateHubspotInformation < Mutations::BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :hubspot_data, GraphQL::Types::JSON, required: true

    def resolve(hubspot_data:)
      context[:current_tenant].update(project_type: hubspot_data['projectType'])
      context[:current_user].reload
      context[:current_user].hubspot_data = { etapa:  hubspot_data['etapa'], lanzamiento: hubspot_data['lanzamiento'] }
      context[:current_user].sync_hubspot_contact
      { success: true }
    end
  end
end
