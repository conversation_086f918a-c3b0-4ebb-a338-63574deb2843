module Mutations
  class Users::UsersBulkUpload < BaseMutation
    field :successes, [Types::Interfaces::UserType], null: false
    field :failures, [GraphQL::Types::JSON], null: false

    argument :users_excel_file, Types::Inputs::UppyFileInputType, required: true

    def resolve(users_excel_file:)
      bom_fix = Proc.new {|headers|
        headers.map do |header|
          new_header = header.force_encoding(Encoding::UTF_8)
          new_header.sub(/^\xEF\xBB\xBF/, '').to_sym
        end
      }

      successes = []
      failures = []
      # retrieve the attachment
      retrieved_file = Shrine::UploadedFile.new(users_excel_file)
      retrieved_file.download do |tempfile|
        users_data = SmarterCSV.process(tempfile, header_transformations: [:none, bom_fix])
        users_data.each do |user_data|
          user_data = user_data.transform_values { |value| value.force_encoding('UTF-8') }
          successes << process_user(**user_data)
        rescue StandardError => e
          failures << { email: user_data[:email], errorMessage: e.message }
        end
      end
      # process the attachment
      # return the successes and failures
      { successes:, failures:}
    end

    def process_user(first_name:, last_name:, email:, password:, role:, phone: nil, group: nil, **custom_fields)
      type = role.upcase_first
      create_user_hash = {
        first_name:,
        last_name:,
        email:,
        password:,
        phone:,
        type:,
        custom_fields:,
      }
      new_user = User.create!(**create_user_hash)
      # Buscar grupo
      if group.present?
        group_name = group.squish
        group_model = ::Groups::Group.find_by('name ILIKE ?', group_name) || ::Groups::Group.create!(name: group_name)
        new_user.groups << group_model
      end

      new_user
    end
  end
end
