module Mutations
  class Users::UpdateUserActivity < BaseMutation
    description "Creates a new user activity entry"
    field :success, <PERSON><PERSON><PERSON>, null: true

    argument :activity_input, Types::Inputs::UserActivityInputType, required: true

    def resolve(activity_input:)
      last_day_activity = Time.zone.now

      activity = context[:current_user].user_activities.find_or_initialize_by(activity_date: last_day_activity)

      time_in_hours = activity_input.time.to_f / 60.0

      activity.time_spent_in_hours += time_in_hours

      activity.time_spent_in_hours = [activity.time_spent_in_hours, 24].min

      activity.save!

      { success: true }
    end
  end
end
