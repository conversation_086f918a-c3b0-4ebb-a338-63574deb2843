module Mutations
  module Users
    class UpdateUser < ::Mutations::BaseMutation
      field :user, Types::Interfaces::UserType, null: false

      argument :id, Integer, "The ID of the module to be updated", required: true
      argument :user_input, Types::Inputs::UserInputType, "The user data to be updated", required: true

      def resolve(id:, user_input:)
        user = context[:current_tenant].users.find(id)
        user.update!(**user_input)
        { user: user }
      end
    end
  end
end
