module Mutations
  class DeleteStripeCard < BaseMutation
    
    argument :payment_method_id, String, required: true

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    def resolve(payment_method_id:)
      payment_method = context[:current_tenant].stripe_payment_methods.find { |pm| pm["id"] == payment_method_id }

      if payment_method.present?
        begin
          Stripe.api_key = Rails.application.credentials.stripe.api_secret
          payment_method = Stripe::PaymentMethod.retrieve(payment_method_id)
          if payment_method.customer == context[:current_tenant].stripe_customer_id
            Stripe::PaymentMethod.detach(payment_method_id)
            context[:current_tenant].get_default_payment_method

            return { success: true, errors: [] }
          else
            return { success: false, errors: [] }
          end
        rescue Stripe::StripeError => e
          { success: false, errors: [e.message] }
        end
      else
        { success: false, errors: ["Payment method not found"] }
      end
    end
  end
end
