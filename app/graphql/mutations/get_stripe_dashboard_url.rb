module Mutations
  class GetStripeDashboardUrl < BaseMutation
    field :url, String, null: false

    def resolve
      Stripe.api_key = Rails.application.credentials.stripe.api_secret
      tenant = context[:current_tenant]
      raise GraphQL::ExecutionError, "Esta cuenta no tiene un cliente asociado en Stripe" unless tenant.stripe_customer_id.present?

      session = Stripe::BillingPortal::Session.create({
        customer: tenant.stripe_customer_id,
        return_url: tenant.academy_url + '/home', 
      })
      { url: session.url }
    end
  end
end
