module Mutations
  class GenerateStripeOauthToken < GraphQL::Schema::Mutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :error, String, null: true

    argument :code, String, required: true

    def resolve(code:)
      Stripe.api_key = Rails.application.credentials.stripe.api_secret

      response = Stripe::OAuth.token({
        grant_type: 'authorization_code',
        code: code,
      })

      if response.access_token
        payment_method = context[:current_tenant].payment_methods.find_or_initialize_by(type: 'Purchases::PaymentMethodStripe')
        payment_method.name = 'Stripe'

        payment_method.config = {
          api_key: response.access_token,
          refresh_token: response.refresh_token,
          publishable_key: response.stripe_publishable_key,
          stripe_user_id: response.stripe_user_id,
        }
        {
          success: payment_method.save,
          error: nil
        }
      else
        {
          success: false,
          error: "Failed to exchange the authorization code for a token."
        }
      end
    end
  end
end
