module Mutations
    class CreateCheckoutSession < BaseMutation
      field :checkout_url, String, null: false
      argument :provider, String, required: false
  
      def resolve(provider:)
        Stripe.api_key = Rails.application.credentials.stripe.api_secret
        tenant = context[:current_tenant]
        session_args = {
            mode: 'setup',
            payment_method_types: ['card'],
            success_url: "#{tenant.academy_url}/callbacks/stripe_credit_card_success?session_id={CHECKOUT_SESSION_ID}",
            cancel_url: "#{tenant.academy_url}/callbacks/stripe_credit_card_cancel",
        }

        if tenant.stripe_customer_id
          session_args[:customer] = tenant.stripe_customer_id
        else
          full_name, email = nil
          without_tenant_protection do
            super_admin = tenant.super_admin
            full_name = super_admin.full_name
            email =  super_admin.email
          end
          customer = Stripe::Customer.create(name: full_name, email: email)
          session_args[:customer] = customer.id
          tenant.update(stripe_customer_id:  customer.id)
        end
        session = Stripe::Checkout::Session.create(**session_args)

        { checkout_url: session.url }
      end
    end
  end
  