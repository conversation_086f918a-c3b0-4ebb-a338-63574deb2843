# frozen_string_literal: true

module Mutations
  class Media::LessonContentDelete < BaseMutation
    description "Deletes one or many lesson_contents by their ids"

    field :lesson_content_items, [Types::Objects::Media::LessonContentType], null: false

    argument :ids, [Integer], required: true

    def resolve(ids:)
      media_lesson_content_items = ::Media::LessonContent.where(id: ids).destroy_all

      { lesson_content_items: media_lesson_content_items }
    end
  end
end
