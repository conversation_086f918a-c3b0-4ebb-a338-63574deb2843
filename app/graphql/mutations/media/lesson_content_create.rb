# frozen_string_literal: true

module Mutations
  class Media::LessonContentCreate < BaseMutation
    description "Creates a new lesson_content"

    field :lesson_content, Types::Objects::Media::LessonContentType, null: false

    argument :lesson_content_input, Types::Inputs::Media::LessonContentInputType, required: true

    def resolve(lesson_content_input:)
      media_lesson_content = ::Media::LessonContent.new(**lesson_content_input)
      raise GraphQL::ExecutionError.new "Error creating lesson_content", extensions: media_lesson_content.errors.to_hash unless media_lesson_content.save

      { lesson_content: media_lesson_content }
    end
  end
end
