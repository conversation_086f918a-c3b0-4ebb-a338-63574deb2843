module Mutations
    class DeleteNotice < BaseMutation
      field :notice, Types::Objects::NoticeType, null: false
      argument :id, ID, required: true
  
      def resolve(id:)
        notice = Notice.find(id)
        raise GraphQL::ExecutionError.new "Error deleting notice", extensions: notice.errors.to_hash unless notice.destroy

        {
          notice: notice
        }
      end
    end
end