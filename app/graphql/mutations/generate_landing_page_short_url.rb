module Mutations
  class GenerateLandingPageShortUrl < BaseMutation
    include UrlShortener

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :short_url, String, null: true
    field :errors, [String], null: false

    argument :id, ID, required: false
    argument :type, String, required: false
    argument :long_url, String, required: false

    def resolve(id:, type:, long_url:)
      if type == 'products'
        object = ::Courses::Course.find(id)
      elsif type == 'packs'
        object = ::Courses::Pack.find(id)
      elsif type == 'communities'
        object = ::Communities::Community.find(id)
      else
        return { success: false, errors: ["Invalid type"] }
      end

      short_url = object.tiny_url.present? ? object.tiny_url : generate_short_url(long_url)
        
      if short_url
        without_tenant_protection do
          object.update(tiny_url: short_url)
        end
        { success: true, short_url: short_url, errors: [] }
      else
        { success: false, errors: ["Failed to generate short URL"] }
      end

    end
  end
end
