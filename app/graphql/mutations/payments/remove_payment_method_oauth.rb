module Mutations
  class Payments::RemovePaymentMethodOauth < BaseMutation

    field :success, Bo<PERSON>an, null: false
    field :errors, [String], null: false

    argument :type, String, required: true

    def resolve(type:)
      payment_method = context[:current_tenant].payment_methods.find_by(type: type)
      if payment_method.present?
        if type == "Purchases::PaymentMethodPayPal"
          payment_method.update!(access_token: nil, refresh_token: nil, merchant_id: nil, expires_at: nil, one_click_enabled: false)
        elsif type == "Purchases::PaymentMethodMercadoPago"
          payment_method.update!(oauth_access_token: nil, refresh_token: nil, expires_at: nil, oauth_public_key: nil, user_id: nil, one_click_enabled: false)
        end
        { success: true, errors: [] }
      else
        { success: false, errors: ["Payment Method Not Present"] }
      end
    end
  end
end
