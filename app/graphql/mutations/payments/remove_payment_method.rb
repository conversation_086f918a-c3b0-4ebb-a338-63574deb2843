module Mutations
  class Payments::RemovePaymentMethod < BaseMutation

    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    argument :type, String, required: true

    # ⚠️ NOTE: We're not destroying the payment method record.
    # Instead, we are clearing the account_id to disconnect the Stripe account

    def resolve(type:)
      payment_method = context[:current_tenant].payment_methods.find_by(type:)
      if payment_method.present?
        if type == "Purchases::PaymentMethodStripeConnect" && payment_method.account_id.present?
          payment_method.update!(account_id: nil, removed_account_id: payment_method.account_id)
        end
        { success: true, errors: [] }
      else
        { success: false, errors: ["Payment Method Not Present"] }
      end
    end
  end
end
