module Mutations
  class CustomSignUpFormEdit < BaseMutation
    field :custom_fields_config, GraphQL::Types::JSON, null: false

    argument :custom_fields_config, GraphQL::Types::JSON, required: false

    def resolve(custom_fields_config:)
      tenant = context[:current_tenant]
      tenant.update!(custom_fields_config:)
      { custom_fields_config: tenant.custom_fields_config }
    end
  end
end
