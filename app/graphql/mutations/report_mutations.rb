module Mutations
  module ReportMutations
    include Types::BaseInterface

    field :create_report, mutation: Mutations::Reports::CreateReport
    field :update_report, mutation: Mutations::Reports::UpdateReport
    field :delete_report, mutation: Mutations::Reports::DeleteReport
    field :create_report_tab, mutation: Mutations::Reports::CreateReportTab
    field :delete_report_tab, mutation: Mutations::Reports::DeleteReportTab
  end
end
