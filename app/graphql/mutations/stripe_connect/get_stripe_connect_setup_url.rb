module Mutations
  class StripeConnect::GetStripeConnectSetupUrl < BaseMutation
    field :setup_url, String, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    def resolve
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
      payment_method = ::Purchases::PaymentMethodStripeConnect.find_by(name: 'Sabionet Payments')
      account_link = Stripe::AccountLink.create({
        account: payment_method.account_id,
        refresh_url: "#{context[:origin]}/settings",
        return_url: "#{context[:origin]}/settings",
        type: 'account_onboarding',
      })

      { setup_url: account_link.url, success: :true, errors: [] }
    rescue => e
      { setup_url: '', success: false, errors: [e]}
    end
  end
end
