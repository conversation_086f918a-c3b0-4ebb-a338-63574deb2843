module Mutations
  class StripeConnect::GetStripeConnectDashboardUrl < BaseMutation
    field :dashboard_url, String, null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false

    def resolve
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
      payment_method = ::Purchases::PaymentMethodStripeConnect.find_by(name: 'Sabionet Payments')

      login_link = Stripe::Account.create_login_link(payment_method.account_id, { redirect_url: "#{context[:origin]}/settings" })

      { dashboard_url: login_link.url, success: :true, errors: [] }
    rescue => e
      { dashboard_url: '', success: false, errors: [e] }
    end
  end
end
