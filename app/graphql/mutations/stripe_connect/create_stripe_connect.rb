module Mutations
  class StripeConnect::CreateStripeConnect < BaseMutation
    field :checkout_url, String, null: false
    field :success, Boolean, null: false
    field :errors, [String], null: false

    def resolve
      ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
      account_data = {
        type: 'express',
        settings: {
          payouts: {
            schedule: {
              interval: 'daily',
              delay_days: 30
            },
          }
        },
        metadata: {
          tenant_id:  context[:current_tenant].id
        }
      }
      account = Stripe::Account.create(account_data)
      # "#{tenant.academy_url}/callbacks/stripe_credit_card_success?session_id={CHECKOUT_SESSION_ID}"
      payment_method = ::Purchases::PaymentMethodStripeConnect.find_or_initialize_by(name: 'Sabionet Payments')
      payment_method.account_id = account.id
      payment_method.save!

      account_link = Stripe::AccountLink.create({
        account: account.id,
        refresh_url: "#{context[:origin]}/payments",
        return_url: "#{context[:origin]}/payments",
        type: 'account_onboarding',
      })

      { checkout_url: account_link.url, success: :true, errors: [] }
    rescue => e
      { checkout_url: '', success: false, errors: [e]}
    end
  end
end
