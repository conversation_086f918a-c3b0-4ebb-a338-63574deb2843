module Mutations
  module Courses
    class UpdateLessonPdf < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonPdfType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonPdfInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonPdf.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
