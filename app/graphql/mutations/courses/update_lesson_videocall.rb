module Mutations
  module Courses
    class UpdateLessonVideocall < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonVideocallType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonVideocallInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonVideocall.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
