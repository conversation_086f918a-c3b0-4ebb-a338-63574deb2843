module Mutations
  class Courses::CreatePack < BaseMutation
    # TODO: define return fields
    field :pack, Types::Objects::Courses::PackType, null: false

    # TODO: define arguments
    argument :pack_input, Types::Inputs::Courses::PackInputType, required: true

    def resolve(pack_input:)
      pack = ::Courses::Pack.new(tenant: context[:current_tenant], created_by: context[:current_user], **pack_input)
      pack.save!
      {
        pack: pack
      }
    end
  end
end
