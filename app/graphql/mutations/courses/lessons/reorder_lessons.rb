module Mutations
  module Courses
    module Lessons
      class ReorderLessons < ::Mutations::BaseMutation
        description "Moves a lesson to the given position. All other lessons will be moved down"
        field :lessons, [Types::Objects::Courses::LessonType], null: false

        argument :id, Integer, "The ID of the lesson to be repositioned", required: true
        argument :position, Integer, "The new position of the lesson", required: true

        def resolve(id:, position:)
          item = ::Courses::Lesson.where(tenant: context[:current_tenant]).find(id)
          item.insert_at(position)
          { lessons: ::Courses::Lesson.where(my_module_id: item.my_module_id).order(position: :asc) }
        end
      end
    end
  end
end
