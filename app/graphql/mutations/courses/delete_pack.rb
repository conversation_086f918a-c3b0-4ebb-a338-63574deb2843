module Mutations
  module Courses
    class DeletePack < ::Mutations::BaseMutation
      field :pack, Types::Objects::Courses::PackType, null: false

      argument :id, Integer, "The ID of the pack to be deleted", required: true
      argument :force, <PERSON><PERSON><PERSON>, "If all references to the pack should be destroyed", required: false

      def resolve(id:, force: false)
        item = ::Courses::Pack.find(id)
        if force
          ActiveRecord::Base.transaction do
            item.student_course_accesses.destroy_all
            item.destroy!
          end
        else
          item.destroy!
        end
        { pack: item }
      end
    end
  end
end
