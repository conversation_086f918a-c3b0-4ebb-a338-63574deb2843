module Mutations
  module Courses
    class UpdateLessonVideo < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonVideoType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonVideoInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonVideo.find(id)
        content_id = lesson_input[:content_id] || lesson_input['content_id']
        if content_id.present? && content_id&.to_i != lesson.content_id
          # BunnyCdn::RemoveVideoJob.perform_later(lesson.content)
        end

        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
