module Mutations
  module Courses
    class UpdateCourse < ::Mutations::BaseMutation
      field :course, Types::Objects::Courses::CourseType, null: false

      argument :id, Integer, "The ID of the module to be updated", required: true
      argument :course_input, Types::Inputs::Courses::CourseInputType, "The course data to be updated", required: true

      def resolve(id:, course_input:)
        course_input_hash = course_input.to_h
        marketplace_categories_ids = course_input_hash.delete(:marketplace_categories_ids)
        affiliate_setting = course_input_hash.delete(:affiliate_setting)

        course = ::Courses::Course.find(id)
        course.update!(**course_input_hash)
  
        # Assign affiliate_setting
        if course.is_available_on_affiliate
          if course.affiliate_setting.present?
            course.affiliate_setting.update(affiliate_setting)
          else
            course.build_affiliate_setting(affiliate_setting)
            course.save
          end
        end

        if marketplace_categories_ids.present? 
          course.marketplace_category_ids = marketplace_categories_ids
          license = ::Marketplaces::License.where(licensed_by_course: course.id).last

          license_type = course.is_free ? :free : :paid
          
          unless license.present?
            # creando licencia del curso
            license_name = "Marketplace License for #{course.name}"

            license = ::Marketplaces::License.create(
              course_id: course.id,
              license_type: license_type,
              licensed_by_course: course.id,
              licensed_by_tenant: course.tenant_id,
              name: license_name,
            )

            if license_type.eql?(:paid)
              price = course.prices.where(currency: "USD").last

              #creando precio de la licencia
              license_price = price.dup
              license_price.product = license
              license_price.save!
            end
          else
            license.update(license_type:) 
          end 
        end

        { course: course }
      end
    end
  end
end
