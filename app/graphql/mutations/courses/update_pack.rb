module Mutations
  module Courses
    class UpdatePack< ::Mutations::BaseMutation
      field :pack, Types::Objects::Courses::PackType, null: false

      argument :id, Integer, "The ID of the pack to be updated", required: true
      argument :pack_input, Types::Inputs::Courses::PackInputType, "The pack data to be updated", required: true

      def resolve(id:, pack_input:)
        item = ::Courses::Pack.find(id)
        without_tenant_protection do
          item.update!(**pack_input)
        end
        { pack: item }
      end
    end
  end
end
