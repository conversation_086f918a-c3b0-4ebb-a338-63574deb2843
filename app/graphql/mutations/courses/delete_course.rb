module Mutations
  module Courses
    class DeleteCourse < ::Mutations::BaseMutation
      field :course, Types::Objects::Courses::CourseType, null: false

      argument :id, Integer, "The ID of the course to be deleted", required: true

      def resolve(id:)
        course = ::Courses::Course.find(id)
        raise GraphQL::ExecutionError.new "Error deleting comment", extensions: course.errors.to_hash unless course.destroy
  
        { course: course }
      end
    end
  end
end
