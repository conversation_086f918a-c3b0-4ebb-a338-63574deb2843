module Mutations
  class Courses::UpdateCourseActivity < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: true

    argument :activity_input, Types::Inputs::Courses::CourseActivityInputType, required: true

    def resolve(activity_input:)
      subscription_id = activity_input.subscription_id
      subscription =  context[:current_user].subscriptions.find(subscription_id)
      time_in_hours = activity_input.time.to_f / 60.0
      subscription.time_spent_in_hours += time_in_hours
      subscription.time_spent_in_hours = [subscription.time_spent_in_hours, 24].min
      subscription.save!
      { success: true }
    end
  end
end
