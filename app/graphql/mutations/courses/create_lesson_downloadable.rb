module Mutations
  class Courses::CreateLessonDownloadable < BaseMutation
    field :lesson, Types::Objects::Courses::LessonDownloadableType, null: false

    argument :lesson, Types::Inputs::Courses::LessonDownloadableInputType, required: true

    def resolve(lesson:)
      value = ::Courses::LessonDownloadable.create_with_container!(**lesson)
      {
        lesson: value
      }
    end
  end
end
