module Mutations
  module Courses
    module Exams
      class ReorderExamQuestions < ::Mutations::BaseMutation
        description "Moves a question to the given position. All other questions will shift downwards"
        field :exam_question_ids, [Integer], null: false

        argument :id, Integer, "The ID of the question to be repositioned", required: true
        argument :position, Integer, "The new position of the question", required: true

        def resolve(id:, position:)
          item = ::Courses::ExamQuestion.find(id)
          item.insert_at(position)
          { exam_question_ids: ::Courses::ExamQuestion.where(exam_id: item.exam_id).pluck(:id) }
        end
      end
    end
  end
end
