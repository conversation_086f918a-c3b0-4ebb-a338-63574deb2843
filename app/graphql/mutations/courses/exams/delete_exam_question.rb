module Mutations
  class Courses::Exams::DeleteExamQuestion < BaseMutation
    # TODO: define return fields
    field :question, Types::Interfaces::Courses::ExamQuestionType, null: false

    # TODO: define arguments
    argument :id, Integer, "The ID of the exam to be updated", required: true

    def resolve(id:)
      record = ::Courses::ExamQuestion.find(id)
      record.destroy!
      {
        question: record
      }
    end
  end
end
