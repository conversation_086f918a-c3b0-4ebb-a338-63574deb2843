module Mutations
  class Courses::Exams::UpdateLessonExam < BaseMutation
    field :lesson, Types::Objects::Courses::LessonExamType, null: false

    argument :lesson, Types::Inputs::Courses::LessonExamInputType, required: true
    argument :id, Integer, "The ID of the lesson to be updated", required: true

    def resolve(id:, lesson:)
      value = ::Courses::LessonExam.find(id)
      value.update!(**lesson)
      {
        lesson: value
      }
    end
  end
end
