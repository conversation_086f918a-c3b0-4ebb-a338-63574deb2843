module Mutations
  class Courses::Exams::CreateExamQuestion < BaseMutation
    # TODO: define return fields
    field :question, Types::Interfaces::Courses::ExamQuestionType, null: false

    # TODO: define arguments
    argument :question, Types::Inputs::Courses::ExamQuestionInputType, required: true

    def resolve(question:)
      question = ::Courses::ExamQuestion.new(**question)
      question.save!
      {
        question: question
      }
    end
  end
end
