module Mutations
  class Courses::Exams::UpdateExamQuestion < BaseMutation
    # TODO: define return fields
    field :question, Types::Interfaces::Courses::ExamQuestionType, null: false

    # TODO: define arguments
    argument :question, Types::Inputs::Courses::ExamQuestionInputType, required: true
    argument :id, Integer, "The ID of the exam to be updated", required: true

    def resolve(id:, question:)
      record = ::Courses::ExamQuestion.find(id)
      record.update!(**question)
      {
        question: record
      }
    end
  end
end
