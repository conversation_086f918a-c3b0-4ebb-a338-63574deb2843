module Mutations
  module Courses
    class UpdateLessonScorm < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonScormType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonScormInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonScorm.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
