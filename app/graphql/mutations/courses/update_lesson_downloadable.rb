module Mutations
  module Courses
    class UpdateLessonDownloadable < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonDownloadableType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonDownloadableInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonDownloadable.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
