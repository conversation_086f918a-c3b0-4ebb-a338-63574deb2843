module Mutations
  class Courses::Surveys::C<PERSON><PERSON><PERSON><PERSON>Survey < BaseMutation
    field :lesson, Types::Objects::Courses::LessonSurveyType, null: false

    argument :lesson, Types::Inputs::Courses::LessonSurveyInputType, required: true

    def resolve(lesson:)
      value = ::Courses::LessonSurvey.create_with_container!(**lesson)
      {
        lesson: value
      }
    end
  end
end
