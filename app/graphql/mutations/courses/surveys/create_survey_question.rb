module Mutations
  class Courses::Surveys::CreateSurveyQuestion < BaseMutation
    # TODO: define return fields
    field :question, Types::Interfaces::Courses::SurveyQuestionType, null: false

    # TODO: define arguments
    argument :question, Types::Inputs::Courses::SurveyQuestionInputType, required: true

    def resolve(question:)
      question = ::Courses::SurveyQuestion.new(**question)
      question.save!
      {
        question: question
      }
    end
  end
end
