module Mutations
  class Courses::Surveys::Update<PERSON><PERSON>onSurvey < BaseMutation
    field :lesson, Types::Objects::Courses::LessonSurveyType, null: false

    argument :lesson, Types::Inputs::Courses::LessonSurveyInputType, required: true
    argument :id, Integer, "The ID of the lesson to be updated", required: true

    def resolve(id:, lesson:)
      value = ::Courses::LessonSurvey.find(id)
      value.update!(**lesson)
      {
        lesson: value
      }
    end
  end
end
