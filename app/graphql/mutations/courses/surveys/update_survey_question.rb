module Mutations
  class Courses::Surveys::UpdateSurveyQuestion < BaseMutation
    field :question, Types::Interfaces::Courses::SurveyQuestionType, null: false

    argument :question, Types::Inputs::Courses::SurveyQuestionInputType, required: true
    argument :id, Integer, "The ID of the exam to be updated", required: true

    def resolve(id:, question:)
      record = ::Courses::SurveyQuestion.find(id)
      record.update!(**question)
      {
        question: record
      }
    end
  end
end
