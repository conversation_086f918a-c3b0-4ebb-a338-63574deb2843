module Mutations
  class Courses::Surveys::DeleteSurveyQuestion < BaseMutation
    # TODO: define return fields
    field :question, Types::Interfaces::Courses::SurveyQuestionType, null: false

    # TODO: define arguments
    argument :id, Integer, "The ID of the survey to be updated", required: true

    def resolve(id:)
      record = ::Courses::SurveyQuestion.find(id)
      record.destroy!
      {
        question: record
      }
    end
  end
end
