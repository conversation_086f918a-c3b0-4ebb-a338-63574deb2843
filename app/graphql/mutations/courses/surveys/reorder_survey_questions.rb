module Mutations
  module Courses
    module Surveys
      class ReorderSurveyQuestions < ::Mutations::BaseMutation
        description "Moves a question to the given position. All other questions will shift downwards"
        field :survey_question_ids, [Integer], null: false

        argument :id, Integer, "The ID of the question to be repositioned", required: true
        argument :position, Integer, "The new position of the question", required: true

        def resolve(id:, position:)
          item = ::Courses::SurveyQuestion.find(id)
          item.insert_at(position)
          { survey_question_ids: ::Courses::SurveyQuestion.where(survey_id: item.survey_id).pluck(:id) }
        end
      end
    end
  end
end
