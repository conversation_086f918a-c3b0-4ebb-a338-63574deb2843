module Mutations
  module Courses
    class UpdateLessonWebinar < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonWebinarType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonWebinarInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonWebinar.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
