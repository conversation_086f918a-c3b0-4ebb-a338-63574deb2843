module Mutations
  module Courses
    class UpdateLessonHtml < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonHtmlType, null: false

      argument :lesson_input, Types::Inputs::Courses::LessonHtmlInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonHtml.find(id)
        lesson.update!(**lesson_input)
        { lesson: }
      end
    end
  end
end
