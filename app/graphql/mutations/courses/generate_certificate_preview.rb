module Mutations
  class Courses::GenerateCertificatePreview < BaseMutation
    # TODO: define return fields
    field :url, String, null: false

    # TODO: define arguments
    argument :course_id, Integer, required: true

    def resolve(course_id:)
      course = context[:current_tenant].courses.find(course_id)
      certificate_data = course.generate_certificate_preview(context[:current_user])
      course.certificate_preview_data = certificate_data
      course.save
      {
        url: certificate_data.url
      }
    end
  end
end
