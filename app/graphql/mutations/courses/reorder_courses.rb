module Mutations
    class Courses::ReorderCourses < BaseMutation
      # TODO: define return fields
      field :courses, [Types::Objects::Courses::CourseType], null: false

      # TODO: define arguments
      argument :id, Integer, "The ID of the course to be repositioned", required: true
      argument :position, Integer, "The new position of the course", required: true
  
      def resolve(id:, position:)
        course = ::Courses::Course.where(tenant: context[:current_tenant]).find(id)
        course.insert_at(position)
        {
          courses: context[:current_user].courses
        }
      end
    end
end
  

  