module Mutations
  module Courses
    class UpdateLessonAudio < ::Mutations::BaseMutation
      field :lesson, Types::Objects::Courses::LessonAudioType, null: false
      argument :lesson_input, Types::Inputs::Courses::LessonAudioInputType, required: true
      argument :id, Integer, "The ID of the lesson to be updated", required: true
    
      def resolve(id:, lesson_input:)
        lesson = ::Courses::LessonAudio.find(id)
        lesson.update!(**lesson_input)
        { lesson: lesson }
      end
    end
  end
end
