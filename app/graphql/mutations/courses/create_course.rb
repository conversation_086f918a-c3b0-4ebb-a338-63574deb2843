module Mutations
  class Courses::CreateCourse < BaseMutation
    # TODO: define return fields
    field :course, Types::Objects::Courses::CourseType, null: false

    # TODO: define arguments
    argument :course_input, Types::Inputs::Courses::CourseInputType, required: true

    def resolve(course_input:)
      course = ::Courses::Course.new(tenant: context[:current_tenant], created_by: context[:current_user], **course_input)
      course.save!
      {
        course: course
      }
    end
  end
end
