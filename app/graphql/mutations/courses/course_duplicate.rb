module Mutations
  class Courses::CourseDuplicate < BaseMutation
    # TODO: define return fields
    field :new_course, Types::Objects::Courses::CourseType, null: false

    argument :base_course_id, Integer, required: true
    argument :new_name, String, required: true

    def resolve(base_course_id:, new_name:)
      MultiTenantSupport::Current.tenant_account = nil
      MultiTenantSupport.turn_off_protection

      course = ::Courses::Course.find(base_course_id)
      new_tenant = context[:current_tenant]
      
      unless course.tenant == new_tenant
        new_course = course.licenses.first.add_license!(new_tenant:)  # cuando la licencia es free, duplicamos desde aqui
      else
        new_course = course.duplicate!(new_name)
      end
     
      { new_course: }
    end
  end
end
