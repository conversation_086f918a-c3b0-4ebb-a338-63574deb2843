module Mutations
  module Courses
    module Mymodules
      class ReorderMyModules < ::Mutations::BaseMutation
        description "Places a mymodule at the given position. All other mymodules will be moved down"
        field :my_modules, [Types::Objects::Courses::MyModuleType], null: false

        argument :id, Integer, "The ID of the module to be repositioned", required: true
        argument :position, Integer, "The new position of the module", required: true

        def resolve(id:, position:)
          item = ::Courses::MyModule.where(tenant: context[:current_tenant]).find(id)
          item.insert_at(position)
          { my_modules: ::Courses::MyModule.where(course_id: item.course_id).order(position: :asc) }
        end
      end
    end
  end
end
