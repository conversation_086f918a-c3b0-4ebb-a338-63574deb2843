module Mutations
  class Courses::Mymodules::CreateMyModule < BaseMutation
    # TODO: define return fields
    field :mymodule, Types::Objects::Courses::MyModuleType, null: false

    # TODO: define arguments
    argument :mymodule, Types::Inputs::Courses::MyModuleInputType, required: true

    def resolve(mymodule:)
      mymodule = ::Courses::MyModule.new(**mymodule)
      mymodule.save!
      {
        mymodule: mymodule
      }
    end
  end
end
