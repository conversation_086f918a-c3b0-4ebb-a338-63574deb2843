module Mutations
  module Courses
    module Mymodules
        class RemoveMyModules < BaseMutation
        
          field :mymodule, Types::Objects::Courses::MyModuleType, null: false
          # TODO: define arguments
          # argument :name, String, required: true
          argument :id, Integer, required: true
        
          def resolve(id:)
            mymodule = ::Courses::MyModule.find(id)
            mymodule.destroy!
            { mymodule: mymodule }
          end
        end
      end
    end
  end