module Mutations
  module Courses
    module Mymodules
      class UpdateMyModule < ::Mutations::BaseMutation
        field :mymodule, Types::Objects::Courses::MyModuleType, null: false

        argument :id, Integer, "The ID of the module to be updated", required: true
        argument :my_module, Types::Inputs::Courses::MyModuleInputType, "The module data to be updated", required: true

        def resolve(id:, my_module:)
          item = ::Courses::MyModule.find(id)
          item.update!(**my_module)
          { mymodule: item }
        end
      end
    end
  end
end
