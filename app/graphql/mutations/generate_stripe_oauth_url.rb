module Mutations
  class GenerateStripeOauthUrl < GraphQL::Schema::Mutation
    field :oauth_url, String, null: false

    def resolve
      query = {
        response_type: "code",
        client_id: ENV.fetch('STRIPE_OAUTH_CLIENT_ID', ''),
        scope: "read_write",
        state: context[:origin],
        redirect_uri: "#{BASE_URL}/stripe_redirect"
      }.to_query

      { oauth_url: "https://connect.stripe.com/oauth/authorize?#{query}" }
    end
  end
end
