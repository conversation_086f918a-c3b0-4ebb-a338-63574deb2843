module Mutations::Admin
    class MarketplacesCategoryUpdate < Mutations::BaseMutation
      description "Updates a category by id"

      field :category, Types::Objects::Marketplaces::CourseCategoryType, null: true

      argument :id, ID, required: true
      argument :category_input, Types::Inputs::Marketplaces::CourseCategoryInputType, required: true

      def resolve(id:, category_input:)
        category_input_hash = category_input.to_h
        main_ids = category_input_hash.delete(:main_ids)

        marketplaces_category = ::Marketplaces::Category.find(id)

        raise GraphQL::ExecutionError.new "Error updating category", extensions: marketplaces_category.errors.to_hash unless marketplaces_category.update(**category_input_hash)

        array = []
        main_ids.each do |main_id|
          main = Marketplaces::Category.find(main_id)
          array.push(main)
        end
        marketplaces_category.main_categories = array

        { category: marketplaces_category }
      end
    end
end
  