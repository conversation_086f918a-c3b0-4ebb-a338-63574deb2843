module Mutations
    class Admin::CreateTemplate < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true
    field :template, Types::Objects::TemplateType, null: false

    argument :template_input, Types::Inputs::Templates::CreateTemplatesInputType, required: true

    def resolve(template_input:)
      template = Template.new(**template_input)
      if template.save
        { success: true, template: template }
      else
        { success: false, errors: template.errors.full_messages, template: template }
      end
    end
  end
end
