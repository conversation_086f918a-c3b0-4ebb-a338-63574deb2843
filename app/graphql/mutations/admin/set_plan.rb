module Mutations
  class Admin::SetPlan < BaseMutation
    field :tenant, Types::Objects::TenantType, null: false

    argument :subdomain, String, required: true
    argument :plan, String, required: true
    argument :feature_plan_expiration_date, GraphQL::Types::ISO8601Date, required: false
    argument :is_trial, Boolean, required: false

    def resolve(subdomain:, plan:, feature_plan_expiration_date: nil, is_trial: nil)
      tenant = Tenant.find_by(subdomain:)
      tenant.set_plan!(plan, feature_plan_expiration_date:, is_trial:)
      tenant.mark_as_customer! if is_trial == false
      tenant.tenant_metric.update(subscription_type: :manually)
      { tenant: }
    end
  end
end

