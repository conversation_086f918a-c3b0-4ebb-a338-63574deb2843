module Mutations
  class Admin::UpdateTemplate < BaseMutation
    field :success, Bo<PERSON>an, null: false
    field :errors, [String], null: true
    field :template, Types::Objects::TemplateType, null: false

    argument :template_input, Types::Inputs::Templates::CreateTemplatesInputType, required: true
    argument :id, ID, required: true

    def resolve(id:, template_input:)
      template = Template.find_by(id:)
      if template.update(**template_input)
        { success: true, template: template }
      else
        { success: false, errors: template.errors.full_messages, template: template }
      end
    end
  end
end
