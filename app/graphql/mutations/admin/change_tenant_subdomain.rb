module Mutations
  class Admin::ChangeTenantSubdomain < BaseMutation
    field :tenant, Types::Objects::TenantType, null: false
    field :results, GraphQL::Types::JSON, null: false

    argument :subdomain, String, required: true
    argument :new_subdomain, String, required: true
    
    def resolve(subdomain:, new_subdomain:)
      tenant = Tenant.find_by!(subdomain:)
      results = MultiTenantSupport.under_tenant tenant do
        tenant.change_subdomain new_subdomain
      end
      { tenant:, results: }
    end
  end
end
