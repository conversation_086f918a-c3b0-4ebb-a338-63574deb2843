module Mutations
  class Admin::UpdateCustomer < BaseMutation
    field :tenant, Types::Objects::TenantType, null: false

    argument :subdomain, String, required: true
    argument :customer, <PERSON><PERSON><PERSON>, required: true
    argument :disabled, <PERSON><PERSON><PERSON>, required: true

    def resolve(subdomain:, customer:, disabled: nil)
      tenant = Tenant.find_by(subdomain:)
      tenant.update(disabled:)
      if disabled
        tenant.tenant_metric.update!(is_customer: false)
      else
        tenant.tenant_metric.update!(is_customer: customer)
      end
      { tenant: }
    end
  end
end
