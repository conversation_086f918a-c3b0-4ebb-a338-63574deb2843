module Mutations
  module Admin
    class DeleteTenants < BaseMutation
      argument :tenant_ids, [ID], required: true
      field :success, <PERSON><PERSON><PERSON>, null: false
      field :errors, [String], null: true

      def resolve(tenant_ids:)
        unless Tenant.exists?(id: tenant_ids)
          return { success: false, errors: ["No tenants found with the provided IDs."] }
        end

        ::TenantCleanupJob.perform_later(ids: tenant_ids)
        
        { success: true, message: "Deletion job processing in background."}
      rescue => e
        raise GraphQL::ExecutionError.new("Error deleting tenants: #{e.message}")
      end
    end
  end
end
