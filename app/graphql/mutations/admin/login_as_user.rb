module Mutations
  class Admin::LoginAsUser < BaseMutation
    description "Authenticates a super admin and redirects them to their subdomain"

    field :auth_headers, GraphQL::Types::JSON, "The authentication headers", null: false
    field :academy_url, String, "The URL of the academy", null: false

    argument :subdomain, String, required: true
    argument :user_type, String, required: true

    def resolve(subdomain:, user_type:)
      tenant = Tenant.find_by!(subdomain: subdomain)
      MultiTenantSupport.under_tenant tenant do
        auth_headers =
          case user_type
          when "SuperAdmin"
            without_tenant_protection do
              tenant.super_admin.create_new_auth_token
            end
          when "Student"
            Student.create_with(
              first_name: "Estudiante Prueba",
              last_name: "Sabionet #{subdomain}",
              password: SecureRandom.hex
            ).find_or_create_by!(email: "estudianteprueba+#{subdomain}@sabionet.com").create_new_auth_token
          else
            raise GraphQL::ExecutionError, "Invalid user type"
          end
        {
          academy_url: tenant.academy_url,
          auth_headers:
        }
      end
    end
  end
end
