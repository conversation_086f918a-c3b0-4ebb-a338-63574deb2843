module Mutations::Admin
    class MarketplacesCategoryCreate< Mutations::BaseMutation
      field :category, Types::Objects::Marketplaces::CourseCategoryType, null: false
      argument :category_input, Types::Inputs::Marketplaces::CourseCategoryInputType, required: true
      
      def resolve(category_input:)
        category_input_hash = category_input.to_h
        main_ids = category_input_hash.delete(:main_ids)

        marketplaces_category = Marketplaces::Category.new(**category_input_hash)
        raise GraphQL::ExecutionError.new "Error creating category", extensions: marketplaces_category.errors.to_hash unless marketplaces_category.save

        if main_ids.count > 0
          main_ids.each do |main_id|
            main = Marketplaces::Category.find(main_id)
            Marketplaces::RelationCategory.create(main_id: main_id, branch_id: marketplaces_category.id)
          end
        end

        { category: marketplaces_category }
      end
    end
end