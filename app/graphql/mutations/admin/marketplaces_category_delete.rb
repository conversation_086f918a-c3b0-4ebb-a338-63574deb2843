module Mutations::Admin
    class MarketplacesCategoryDelete< Mutations::BaseMutation
      description "Deletes a category by ID"

      field :category, Types::Objects::Marketplaces::CourseCategoryType, null: true
      argument :id, ID, required: true
  
      def resolve(id:)
        marketplaces_category = ::Marketplaces::Category.find(id)
        raise GraphQL::ExecutionError.new "Error deleting category", extensions: purchases_price.errors.to_hash unless marketplaces_category.destroy
  
        { category: marketplaces_category }
      end
    end
end
  