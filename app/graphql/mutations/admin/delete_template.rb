module Mutations
  class Admin::DeleteTemplate < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: true

    argument :id, ID, required: true

    def resolve(id:)
      template = Template.find_by(id:)
      if template.destroy
        { success: true }
      else
        { success: false, errors: template.errors.full_messages }
      end
    end
  end
end
