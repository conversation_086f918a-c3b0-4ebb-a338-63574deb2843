module Mutations::Admin
  class EditTenant < Mutations::BaseMutation
    field :tenant, Types::Objects::TenantType, null: false

    argument :subdomain, String, required: true
    argument :tenant_input, Types::Inputs::TenantInputType, required: true

    def resolve(subdomain:, tenant_input:)
      tenant = Tenant.find_by!(subdomain:)
      tenant.update!(**tenant_input)
      { tenant: }
    end
  end
end
