module Mutations::Admin
  class EditSuperAdmin < Mutations::BaseMutation
    field :super_admin, Types::Objects::SuperAdminType, null: false

    argument :subdomain, String, required: true
    argument :email, String, required: false
    argument :password, String, required: false
    argument :send_email, <PERSON><PERSON><PERSON>, required: false

    def resolve(subdomain:, email: nil, password: nil, send_email: nil)
      super_admin = Tenant.find_by!(subdomain: subdomain).super_admin
      super_admin.update!(email:) if(email)
      super_admin.password_reset!(password, send_email:) if password
      { super_admin: }
    end
  end
end
