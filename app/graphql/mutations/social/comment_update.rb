# frozen_string_literal: true

module Mutations
  class Social::CommentUpdate < BaseMutation
    description "Updates a comment by id"

    field :comment, Types::Objects::Social::CommentType, null: false

    argument :id, Integer, required: true
    argument :comment_input, Types::Inputs::Social::CommentInputType, required: true

    def resolve(id:, comment_input:)
      social_comment = ::Social::Comment.find(id)
      without_tenant_protection do
        raise GraphQL::ExecutionError.new "Error updating comment", extensions: social_comment.errors.to_hash unless social_comment.update(**comment_input)
      end

      { comment: social_comment }
    end
  end
end
