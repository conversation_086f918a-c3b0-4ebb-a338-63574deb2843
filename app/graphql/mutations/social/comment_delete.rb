# frozen_string_literal: true

module Mutations
  class Social::CommentDelete < BaseMutation
    description "Deletes a comment by ID"

    field :comment, Types::Objects::Social::CommentType, null: false

    argument :id, Integer, required: true

    def resolve(id:)
      social_comment = ::Social::Comment.where(tenant: context[:current_tenant]).find(id)
      raise GraphQL::ExecutionError.new "Error deleting comment", extensions: social_comment.errors.to_hash unless social_comment.destroy

      { comment: social_comment }
    end
  end
end
