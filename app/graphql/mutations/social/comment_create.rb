# frozen_string_literal: true

module Mutations
  class Social::CommentCreate < BaseMutation
    description "Creates a new comment"

    field :comment, Types::Objects::Social::CommentType, null: false

    argument :comment_input, Types::Inputs::Social::CommentInputType, required: true

    def resolve(comment_input:)
      social_comment = ::Social::Comment.new(tenant: context[:current_tenant], author: context[:current_user], **comment_input)
      raise GraphQL::ExecutionError.new "Error creating comment", extensions: social_comment.errors.to_hash unless social_comment.save

      { comment: social_comment }
    end
  end
end
