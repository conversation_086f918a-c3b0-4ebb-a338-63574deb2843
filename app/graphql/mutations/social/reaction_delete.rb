# frozen_string_literal: true

module Mutations
  class Social::ReactionDelete < BaseMutation
    description "Deletes a reaction by ID"

    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :reaction_input, Types::Inputs::Social::ReactionInputType, required: true

    def resolve(reaction_input:)
      social_reaction = ::Social::Reaction.find_by(**reaction_input, author: context[:current_user])
      return { success: false } if social_reaction.nil?

      raise GraphQL::ExecutionError.new "Error deleting reaction", extensions: social_reaction.errors.to_hash unless social_reaction.destroy

      { success: true }
    end
  end
end
