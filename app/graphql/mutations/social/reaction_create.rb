# frozen_string_literal: true

module Mutations
  class Social::ReactionCreate < BaseMutation
    description "Creates a new reaction"

    field :reaction, Types::Enums::Social::ReactionTypeType, null: false

    argument :reaction_input, Types::Inputs::Social::ReactionInputType, required: true

    def resolve(reaction_input:)
      social_reaction = ::Social::Reaction.new(**reaction_input, author: context[:current_user], tenant: context[:current_tenant])
      raise GraphQL::ExecutionError.new "Error creating reaction", extensions: social_reaction.errors.to_hash unless social_reaction.save

      { reaction: social_reaction.type }
    end
  end
end
