module Mutations
  class Communities::ReorderCommunities < BaseMutation
    field :communities, [Types::Objects::Communities::CommunityType], null: false

    argument :id, Integer, "The ID of the community to be repositioned", required: true
    argument :position, Integer, "The new position of the community", required: true

    def resolve(id:, position:)
      community = ::Communities::Community.where(tenant: context[:current_tenant]).find(id)
      community.insert_at(position)
      {
        communities: context[:current_user].communities
      }
    end
  end
end
