module Mutations
  class Communities::UpdateCommunityPost < ::Mutations::BaseMutation
    field :community_post, Types::Objects::Communities::CommunityPostType, null: true
    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: true

    argument :id, Integer, "The ID of the community post to be updated", required: true
    argument :community_id, Integer, required: true
    argument :community_post_input, Types::Inputs::Communities::CommunityPostInputType, "The community post data to be updated", required: true

    def resolve(id:, community_id:, community_post_input:)
      community = context[:current_tenant].communities.find(community_id)

      community_post = community.community_posts.find(id)
      if community_post.user_id == context[:current_user].id
        without_tenant_protection do
          community_post.update!(**community_post_input)
        end
        { community_post: community_post, success: true, errors: [] }
      else
        raise 'Unauthorized'
      end
    rescue => e
      { success: false, errors: [e.message], community_post: {} }
    end
  end
end
