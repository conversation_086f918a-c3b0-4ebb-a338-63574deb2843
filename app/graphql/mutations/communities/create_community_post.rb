module Mutations
  class Communities::CreateCommunityPost < BaseMutation

    field :community_post, Types::Objects::Communities::CommunityPostType, null: true
    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: true

    argument :community_post_input, Types::Inputs::Communities::CommunityPostInputType, required: true
    argument :community_id, Integer, required: true

    def resolve(community_id:, community_post_input:)
      community = ::Communities::Community.find(community_id)

      community_post = community.community_posts.new(user: context[:current_user], **community_post_input)

      if community_post.save
        {
          success: true,
          community_post: community_post,
          errors: []
        }
      else
        {
          success: false,
          community_post: community_post,
          errors: community_post.errors.full_messages
        }
      end
    rescue =>  e
      { success: false, errors: [e], community_post: {} }
    end
  end
end
