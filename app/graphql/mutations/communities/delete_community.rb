module Mutations
  class Communities::DeleteCommunity < ::Mutations::BaseMutation
    field :community, Types::Objects::Communities::CommunityType, null: false

    argument :id, Integer, "The ID of the community to be deleted", required: true

    def resolve(id:)
      community = ::Communities::Community.find(id)
      raise GraphQL::ExecutionError.new "Error deleting community", extensions: community.errors.to_hash unless community.destroy

      { community: community }
    end
  end
end
