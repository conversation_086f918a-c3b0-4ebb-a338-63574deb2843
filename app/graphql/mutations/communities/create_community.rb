module Mutations
  class Communities::CreateCommunity < BaseMutation

    field :community, Types::Objects::Communities::CommunityType, null: false

    argument :community_input, Types::Inputs::Communities::CommunityInputType, required: true

    def resolve(community_input:)
      community = ::Communities::Community.new(tenant: context[:current_tenant], created_by: context[:current_user], **community_input)
      community.save!
      {
        community: community
      }
    end
  end
end
