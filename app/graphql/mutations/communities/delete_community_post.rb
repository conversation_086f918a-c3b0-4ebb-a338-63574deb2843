module Mutations
  class Communities::DeleteCommunityPost < ::Mutations::BaseMutation
    field :community_post, Types::Objects::Communities::CommunityPostType, null: true
    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: true

    argument :id, Integer, "The ID of the community post to be deleted", required: true

    def resolve(id:)
      case context[:current_user]
      when ::Admin, SuperAdmin
        community_post = context[:current_tenant].community_posts.find(id)
        community_post.destroy
      when ::Instructor
        community_post = context[:current_user].dictated_community_posts.find(id)
        community_post.destroy
      when ::Student
        community_post = context[:current_user].community_posts.where(user_id: context[:current_user].id).find(id)
        community_post.destroy
      end
      { success: true, errors: [] }
    rescue => e
      { success: false, errors: [e] }
    end
  end
end
