module Mutations
  class Communities::UpdateCommunity < ::Mutations::BaseMutation
    field :community, Types::Objects::Communities::CommunityType, null: false

    argument :id, Integer, "The ID of the module to be updated", required: true
    argument :community_input, Types::Inputs::Communities::CommunityInputType, "The community data to be updated", required: true

    def resolve(id:, community_input:)
      community_input_hash = community_input.to_h
      community = ::Communities::Community.find(id)
      community.update!(**community_input_hash)
      
      { community: community }
    end
  end
end
