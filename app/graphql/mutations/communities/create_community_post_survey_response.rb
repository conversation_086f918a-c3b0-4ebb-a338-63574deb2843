module Mutations
  class Communities::CreateCommunityPostSurveyResponse < BaseMutation
    field :survey_response, Types::Objects::Communities::CommunityPostSurveyResponseType, null: true
    field :success, <PERSON><PERSON><PERSON>, null: true
    field :errors, [String], null: true

    argument :survey_response_input, Types::Inputs::Communities::CommunityPostSurveyResponseInputType, required: true

    def resolve(survey_response_input:)

      community_post = ::Communities::CommunityPost.find_by(id: survey_response_input[:community_post_id])
      return error_response("Community post not found") unless community_post

      existing_response = community_post.survey_responses.find_by(user: context[:current_user])

      if existing_response.present?
        update_existing_response(existing_response, survey_response_input)
      else
        create_new_response(community_post, survey_response_input)
      end
    rescue StandardError => e
      error_response(e.message)
    end

    private

    def update_existing_response(existing_response, response_data)
      existing_response.update(**response_data)
      success_response(existing_response.reload)
    rescue StandardError => e
      error_response(e.message, existing_response)
    end

    def create_new_response(community_post, response_data)
      survey_response = community_post.survey_responses.build(
        user: context[:current_user],
        **response_data
      )

      if survey_response.save
        success_response(survey_response)
      else
        error_response(survey_response.errors.full_messages, survey_response)
      end
    end

    def success_response(survey_response)
      {
        success: true,
        survey_response: survey_response,
        errors: []
      }
    end

    def error_response(message_or_messages, survey_response = nil)
      {
        success: false,
        survey_response: survey_response,
        errors: Array(message_or_messages)
      }
    end
  end
end
