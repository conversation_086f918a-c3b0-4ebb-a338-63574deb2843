module Mutations
  class CreateWebsiteFooterColumn < BaseMutation
    description "Creates a website footer column"

    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: false

    argument :website_footer_id, Integer, required: true, description: "ID of the website footer"
    argument :column_input, Types::Inputs::WebsiteFooterColumnInputType, required: true

    def resolve(website_footer_id:, column_input:)
      website_footer = context[:current_tenant].website_footers.find(website_footer_id)
      
      footer_column = website_footer.footer_columns.build(
        tenant: context[:current_tenant],
        **column_input
      )
      
      raise GraphQL::ExecutionError.new "Error creating footer column", extensions: footer_column.errors.to_hash unless footer_column.save

      { footer_column: footer_column }
    end
  end
end
