module Mutations
  class Reports::CreateReport < BaseMutation
    field :success, <PERSON><PERSON>an, null: false
    field :report, Types::Objects::Reports::ReportType, null: false

    argument :report_input, Types::Inputs::Reports::ReportInputType, required: true

    def resolve(report_input:)
      report = ::Report.new(report_input.to_h)
      { success: report.save, report: report }
    end
  end
end
