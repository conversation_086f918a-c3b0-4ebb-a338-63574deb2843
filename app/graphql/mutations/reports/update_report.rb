module Mutations
  class Reports::UpdateReport < BaseMutation
    field :success, Bo<PERSON>an, null: false
    field :report, Types::Objects::Reports::ReportType, null: false

    argument :id, ID, required: true
    argument :report_input, Types::Inputs::Reports::ReportInputType, required: true

    def resolve(id:, report_input:)
      report = ::Report.find(id)
      if report.update(report_input.to_h)
        { success: true, report: report  }
      else
        { success: false, report: report  }
      end
    end
  end
end
