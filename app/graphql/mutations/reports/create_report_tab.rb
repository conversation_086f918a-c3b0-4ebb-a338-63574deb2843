module Mutations
  class Reports::CreateReportTab < BaseMutation
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :report_tab, Types::Objects::Reports::ReportTabType, null: false

    argument :report_tab_input, Types::Inputs::Reports::ReportTabInputType, required: true

    def resolve(report_tab_input:)
      report_tab = ::ReportTab.new(report_tab_input.to_h)
      { success: report_tab.save, report_tab: report_tab }
    end
  end
end
