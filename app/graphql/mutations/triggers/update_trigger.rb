module Mutations
  class Triggers::UpdateTrigger < BaseMutation
    field :trigger, Types::Objects::Triggers::TriggerType, null: false

    argument :trigger_input, Types::Inputs::Triggers::TriggerInputType, required: true

    def resolve(trigger_input:)
      trigger = ::Triggers::Trigger.find(trigger_input[:id])
      trigger.update!(trigger_input.to_h)
      { trigger: trigger }
    end
  end
end
