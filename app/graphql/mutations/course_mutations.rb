module Mutations
  module CourseMutations
    include Types::BaseInterface

    field :create_course, mutation: Mutations::Courses::CreateCourse
    field :update_course, mutation: Mutations::Courses::UpdateCourse
    field :delete_course, mutation: Mutations::Courses::DeleteCourse
    field :reorder_courses, mutation: Mutations::Courses::ReorderCourses

    field :create_pack, mutation: Mutations::Courses::CreatePack
    field :update_pack, mutation: Mutations::Courses::UpdatePack
    field :delete_pack, mutation: Mutations::Courses::DeletePack

    field :update_course_activity, mutation: Mutations::Courses::UpdateCourseActivity

    field :generate_certificate_preview, mutation: Mutations::Courses::GenerateCertificatePreview
  end
end