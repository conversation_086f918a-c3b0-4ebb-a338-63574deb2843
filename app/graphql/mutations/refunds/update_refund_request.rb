module Mutations
  module Refunds
    class UpdateRefundRequest < ::Mutations::BaseMutation
      field :success, <PERSON><PERSON><PERSON>, null: false

      argument :refund_request_input, Types::Inputs::Refunds::RefundRequestInputType, "The global setting data to be updated", required: true

      def resolve(refund_request_input:)
        ::Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')

        status = refund_request_input[:status]
        refund_request_id = refund_request_input[:refund_request_id]
        refund_request = ::Purchases::RefundRequest.find(refund_request_id)

        if status == 'approved'
          response = refund_request.product_purchase.process_refund
          success = response.status == 'succeeded'
          refund_request.approved! if success
          { success: }
        elsif status == 'rejected'
          refund_request.rejected!
          { success: true }
        end
      rescue =>  e
        { success: false }
      end
    end
  end
end
