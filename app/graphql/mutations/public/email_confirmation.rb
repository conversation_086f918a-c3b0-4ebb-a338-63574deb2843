module Mutations
    module Public
      class EmailConfirmation < ::Mutations::BaseMutation
        field :success, Boolean, null: false
  
        argument :email, String, required: true
        argument :code, String, required: true

        def resolve(email:, code:)
          user = SabionetUser.find_by(email:)
          user.confirm if user.confirmation_token.eql? code
          {
            success: user.confirmed?
          }
        end
      end
    end
  end
  