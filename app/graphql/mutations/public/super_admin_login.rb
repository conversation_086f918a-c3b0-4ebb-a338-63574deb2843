module Mutations
  class Public::SuperAdminLogin < BaseMutation
    description "Authenticates a super admin and redirects them to their subdomain"

    field :super_admin_auth_headers, GraphQL::Types::JSON, "The authentication headers", null: false
    field :academy_url, String, "The URL of the academy", null: false
    field :users_count, String, null: true

    argument :email, String, required: true
    argument :password, String, required: true

    def resolve(email:, password:)
      MultiTenantSupport.without_current_tenant do
        MultiTenantSupport.turn_off_protection do
          super_admins = SuperAdmin.where(email:)
          super_admin = super_admins.find { |user| user.valid_password?(password) }
          if super_admin.present?
            tenant = super_admin.tenant
            {
              users_count: super_admins.count,
              academy_url: tenant.academy_url,
              super_admin_auth_headers: super_admin.create_new_auth_token
            }
          else
            raise GraphQL::ExecutionError, "Invalid credentials"
          end
        end
      end
    
    end
  end
end
