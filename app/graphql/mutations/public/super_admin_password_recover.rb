module Mutations
    module Public
      class SuperAdminPasswordRecover < ::Mutations::BaseMutation
        field :success, <PERSON><PERSON><PERSON>, null: false
  
        argument :email, String, "The email of the password to be recovered", required: true
  
        def resolve(email:)
          MultiTenantSupport.without_current_tenant do
            MultiTenantSupport.turn_off_protection do
                super_admin = SuperAdmin.find_by(email:)
                super_admin.password_reset!
                { success: true }
            end
          end
        end
      end
    end
  end
  