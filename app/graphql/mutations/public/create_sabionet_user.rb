module Mutations
    module Public
      class CreateSabionetUser < ::Mutations::BaseMutation
        description "Create sabionet user"
        field :success, <PERSON><PERSON><PERSON>, null: false
  
        argument :sabionet_user_input, Types::Inputs::SabionetUserInputType, required: true
        def resolve(sabionet_user_input:)
          email = sabionet_user_input[:email]&.downcase
          provider = sabionet_user_input[:provider]
          uid = sabionet_user_input[:uid]
          first_name = sabionet_user_input[:first_name]
          password = sabionet_user_input[:password]

          MultiTenantSupport.without_current_tenant do
            MultiTenantSupport.allow_read_across_tenant do
              if provider == 'google_oauth2'
                decoded_token = JWT.decode(sabionet_user_input[:token], nil, false).first
                email = decoded_token['email']
                unless email.present?
                  raise GraphQL::ExecutionError.new 'SuperAdmin already exists', extensions: { code: "VALIDATION_ERROR", errors: { email: "already exists" } } if SuperAdmin.where(email:).exists?
                end
              else
                raise GraphQL::ExecutionError.new 'SuperAdmin already exists', extensions: { code: "VALIDATION_ERROR", errors: { email: "already exists" } } if SuperAdmin.where(email:).exists?
              end
            end
          end

          user = SabionetUser.find_by(email:)
          if user.nil?
            sabionet_user_input_hash = sabionet_user_input.to_h
            sabionet_user_input_hash.delete(:token)
            user = SabionetUser.create(**sabionet_user_input_hash, password: Devise.friendly_token[0, 20])
          else
            user.update(provider:, uid:, password:, first_name:)
            # user.regenerate_confirmation_token if provider == 'email'
          end
          {
            success: user.persisted? 
          }
        end
      end
    end
end
