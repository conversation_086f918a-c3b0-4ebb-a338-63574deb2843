module Mutations
    module Public
      class ResendTokenConfirmation < ::Mutations::BaseMutation
        field :success, <PERSON><PERSON><PERSON>, null: false
  
        argument :email, String, required: true
        def resolve(email:)
          user = SabionetUser.find_by(email:)
          user.regenerate_confirmation_token
          {
            success: user.confirmation_token.present?
          }
        end
      end
    end
end