module Mutations
  class Public::PlatformAdminLogin < BaseMutation
    field :platform_admin, Types::Objects::PlatformAdminType, null: false
    field :auth_token, String, null: false

    argument :email, String, required: true
    argument :password, String, required: true

    def resolve(email:, password:)
      @user = PlatformAdmin.authenticate(email, password)
      if @user
        {
          platform_admin: @user,
          auth_token: JsonWebToken.encode({ email: @user.email })
        }
      else
        raise GraphQL::ExecutionError, "Invalid login"
      end
    end
  end
end
