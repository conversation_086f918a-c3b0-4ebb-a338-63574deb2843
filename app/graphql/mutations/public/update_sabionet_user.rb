module Mutations
    module Public
      class UpdateSabionetUser < ::Mutations::BaseMutation
        description "Update sabionet user"
        field :sabionet_user, Types::Objects::SabionetUserType, null: false

        argument :sabionet_user_input, Types::Inputs::SabionetUserInputType, required: true
        def resolve(sabionet_user_input:)
            user_hash = sabionet_user_input.to_h
            email = user_hash.delete(:email)&.downcase
            user = SabionetUser.find_by(email:)
            raise GraphQL::ExecutionError.new "Error updating sabionet user", extensions: user.errors.to_hash unless user.update(**user_hash)
            
            { sabionet_user: user }
        end
      end
    end
end
