module Mutations
  module Public
    class RequestNewOtp < BaseMutation
      argument :email, String, required: true

      field :success, <PERSON><PERSON><PERSON>, null: false
      field :message, String, null: false

      def resolve(email:)
        user = nil
        without_tenant_protection do
          if context[:current_tenant].present?
            user = User.find_by(email: email, tenant_id: context[:current_tenant].id)
          else
            user = User.find_by(email: email)
          end
        end

        return { success: false, message: "User not found" } unless user

        user.send_verification_email

        { success: true, message: "A new OTP has been sent to your email." }
      rescue StandardError => e
        { success: false, message: "Error: #{e.message}" }
      end
    end
  end
end
