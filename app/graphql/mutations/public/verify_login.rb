module Mutations
  module Public
    class VerifyLogin < BaseMutation
      argument :email, String, required: true
      argument :code, String, required: true

      field :success, <PERSON><PERSON><PERSON>, null: false
      field :message, String, null: false
      field :auth_headers, GraphQL::Types::JSON, null: true

      def resolve(email:, code:)
        user = nil
        without_tenant_protection do
          if context[:current_tenant].present?
            user = User.find_by(email: email, tenant_id: context[:current_tenant].id)
          else
            user = User.find_by(email: email)
          end
        end

        if user.nil?
          return { success: false, message: "User not found." }
        end

        if user.login_verification_code == code
          auth_headers = user.create_new_auth_token
          user.remove_old_session

          user.user_sessions.create!(
            ip_address:  context[:remote_ip],
            user_agent: context[:user_agent],
            expires_at: 30.days.from_now,
            sign_in_token: auth_headers['client']
          )

          user.update!(login_verification_code: nil)

          {
            success: true,
            auth_headers: auth_headers,
            message: "Verification successful! You can now log in."
          }
        else
          { success: false, message: "Invalid verification code." }
        end
      end
    end
  end
end
