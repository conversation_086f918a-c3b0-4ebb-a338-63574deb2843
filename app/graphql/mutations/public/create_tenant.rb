module Mutations
  module Public
    class CreateTenant < ::Mutations::BaseMutation

      description "Creates a new tenant"
      field :super_admin_auth_headers, GraphQL::Types::JSON, null: false

      argument :tenant_input, Types::Inputs::CreateTenantInputType, required: true
      argument :super_admin_input, Types::Inputs::CreateSuperAdminInputType, required: true

      def resolve(tenant_input:, super_admin_input:)
        # MultiTenantSupport.without_current_tenant do
        #   MultiTenantSupport.allow_read_across_tenant do
        #     raise GraphQL::ExecutionError.new 'SuperAdmin already exists', extensions: { code: "VALIDATION_ERROR", errors: { email: "already exists" } } if SuperAdmin.where(email: super_admin_input[:email]).exists?
        #   end
        # end
        ActiveRecord::Base.transaction do
          raise GraphQL::ExecutionError.new 'Academy already exists', extensions: { code: "VALIDATION_ERROR", errors: { email: "already exists" } } if Tenant.where(subdomain: tenant_input[:subdomain]).exists?

          tenant = Tenant.create!(ip_address: context[:ip_address], **tenant_input)
          MultiTenantSupport.under_tenant tenant do
            raise GraphQL::ExecutionError.new 'SuperAdmin already exists', extensions: { code: "VALIDATION_ERROR", errors: { email: "already exists" } } if SuperAdmin.where(email: super_admin_input[:email]).exists?

            super_admin = SuperAdmin.create!(**super_admin_input)
            tenant.create_models
            {
              super_admin_auth_headers: super_admin.create_new_auth_token
            }
          end
        end
      end
    end
  end
end
