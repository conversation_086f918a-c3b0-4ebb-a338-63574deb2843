module Mutations
  class Public::AffiliateGoogleSignUp < BaseMutation

    field :auth_headers, GraphQL::Types::JSON, "The authentication headers", null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :redirect_url, String, null: true

    argument :token, String, required: true

    def resolve(token:)
      MultiTenantSupport.turn_off_protection

      decoded_token = JWT.decode(token, nil, false).first
      email = decoded_token['email']

      users = User.where(email: email)

      if users.any? { |user| user.type.in?(["Instructor", "Admin"]) }
        return {
          auth_headers: {},
          errors: ["Este correo electrónico ya está registrado con otro rol."],
          success: false
        }
      end

      user = Affiliate.where(email: email).first_or_initialize do |user|
        user.provider = "google_oauth2"
        user.uid = email
        user.email = email
        user.default_avatar_url =  decoded_token["picture"]
        user.first_name =  decoded_token["given_name"]
        user.last_name = decoded_token["family_name"]
        user.password = Devise.friendly_token[0, 20]
      end

      if user.new_record? && user.save
        @users = User.where(email: email, type: ['Student', 'SuperAdmin', 'Affiliate'])

        redirect_url = get_redirect_url(user)

        { auth_headers: user.create_new_auth_token, success: true, errors: [], redirect_url: redirect_url }
      elsif user.present? && !user.new_record?
        { auth_headers: {}, success: false, errors: ['Este correo electrónico ya está registrado como afiliado. Utilice otro.'], redirect_url: "" }
      else
        { auth_headers: {}, success: false, errors: ['Not able to sign up with google'], redirect_url: '' }
      end
    end

    def get_redirect_url(user)
      return '' if (user.type != 'Affiliate' && user.tenants.many?) || @users&.many?

      domain, protocol = case Rails.env
                         when 'development' then ['sabionetlocal.com:8080', 'http']
                         when 'staging' then ['sabiolms.com', 'https']
                         when 'production' then ['sabionet.com', 'https']
                         end

      if user.type == 'Affiliate'
        redirect_url = "#{protocol}://centralized.#{domain}/affiliate/external-login"
      else
        redirect_url = "#{protocol}://#{user.tenant.subdomain}.#{domain}/external-login"
      end
      redirect_url
    end
  end
end
