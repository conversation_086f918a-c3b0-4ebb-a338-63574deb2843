module Mutations
  class Public::CentralizedGoogleLogin < BaseMutation

    field :auth_headers, GraphQL::Types::JSON, "The authentication headers", null: false
    field :success, <PERSON><PERSON><PERSON>, null: false
    field :errors, [String], null: false
    field :redirect_url, String, null: true

    argument :token, String, required: true

    def resolve(token:)
      MultiTenantSupport.turn_off_protection

      decoded_token = JWT.decode(token, nil, false).first
      email = decoded_token['email']

      @users = User.where(email: email, type: ['Student', 'SuperAdmin', 'Affiliate'])
      user = @users.first

      if user.present?
        redirect_url = get_redirect_url(user)

        { auth_headers: user.create_new_auth_token, success: true, errors: [], redirect_url: redirect_url }
      else
        { auth_headers: {}, success: false, errors: ['User not found with google'], redirect_url: '' }
      end
    end

    def get_redirect_url(user)
      return '' if (user.type != 'Affiliate' && user.tenants.many?) || @users&.many?

      domain, protocol = case Rails.env
                         when 'development' then ['sabionetlocal.com:8080', 'http']
                         when 'staging' then ['sabiolms.com', 'https']
                         when 'production' then ['sabionet.com', 'https']
                         end

      if user.type == 'Affiliate'
        redirect_url = "#{protocol}://centralized.#{domain}/affiliate/external-login"
      else
        redirect_url = "#{protocol}://#{user.tenant.subdomain}.#{domain}/external-login"
      end
      redirect_url
    end
  end
end
