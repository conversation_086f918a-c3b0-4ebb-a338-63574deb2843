module Mutations
  module Public
    class PasswordRecover < ::Mutations::BaseMutation
      field :success, <PERSON><PERSON><PERSON>, null: false

      argument :email, String, "The email of the password to be recovered", required: true

      def resolve(email:)
        user = context[:current_tenant].users.find_by!(email: email)
        user.password_reset!
        { success: true }
      end
    end
  end
end
