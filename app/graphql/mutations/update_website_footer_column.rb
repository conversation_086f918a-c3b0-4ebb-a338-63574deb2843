module Mutations
  class UpdateWebsiteFooterColumn < BaseMutation
    description "Updates a website footer column"

    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: false

    argument :id, Integer, required: true, description: "ID of the footer column"
    argument :column_input, Types::Inputs::WebsiteFooterColumnInputType, required: true

    def resolve(id:, column_input:)
      footer_column = context[:current_tenant].website_footer_columns.find(id)
      
      raise GraphQL::ExecutionError.new "Error updating footer column", extensions: footer_column.errors.to_hash unless footer_column.update(**column_input)

      { footer_column: footer_column }
    end
  end
end
