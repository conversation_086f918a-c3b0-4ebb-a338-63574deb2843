module Queries
  module SocialQueryType
    include Types::BaseInterface

    field :comment_list, Types::Objects::Social::CommentType.connection_type, "The comments associated with a commentable entity", null: false do
      argument :commentable_id, Integer, "The id of the entity", required: true
      argument :commentable_type, Types::Enums::Social::CommentableTypeType, "The type of the entity", required: true
    end

    def comment_list(commentable_id:, commentable_type:)
      Social::Comment.where(tenant: context[:current_tenant], commentable_id: commentable_id, commentable_type: commentable_type)
    end
  end
end
