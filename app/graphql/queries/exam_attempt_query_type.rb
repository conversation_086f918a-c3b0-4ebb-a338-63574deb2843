module Queries
  module ExamAttemptQueryType
    include Types::BaseInterface

    field :get_exam_attempt, Types::Objects::Subscriptions::ExamAttemptType, "A single exam attempt by ID", null: false do
      argument :id, Integer, "The exam attempt ID", required: true
    end

    def get_exam_attempt(id:)
      context[:current_tenant].exam_attempts.find(id)
    end

    field :exam_attempt_list, Types::Objects::ExamAttemptPaginationListType, "A list exam attempts by search string", null: false do
      argument :course_ids, [Integer], "Course filters to get exam attempts", required: false
      argument :statuses, [String], "Statuses to get exam attempts", required: false
      argument :name, String, "Search string to get exam attempts", required: false
      argument :exam_id, Integer, "Filter by exam ID to get exam attempts", required: false
      argument :current_page, Integer, required: false
      argument :search, String, required: false
      argument :status, String, required: false
      argument :page_name, String, required: false
    end

    def exam_attempt_list(course_ids: nil, statuses: nil, name: nil, exam_id: nil, current_page: nil, search: nil, status: nil, page_name: nil)
      attempts = case context[:current_user]
      when ::Admin, SuperAdmin
        context[:current_tenant].exam_attempts
                                .with_courses(course_ids).with_statuses(statuses)
                                .by_student_name(name).by_exam(exam_id)
      when Instructor
        course_ids = context[:current_user].dictated_course_ids
        subscriptions = Subscriptions::Subscription.where(course_id: course_ids)
        exam_attempts = Subscriptions::ExamAttempt.where(subscription_id: subscriptions.ids).by_exam(exam_id)
        all_ids = context[:current_user].exam_attempts.ids + exam_attempts.ids
        Subscriptions::ExamAttempt.where(id: all_ids)
      when Student
        # We are including exam_answers and exams since the UI always requests for it.
        context[:current_user].exam_attempts
                              .with_courses(course_ids).with_statuses(statuses)
                              .by_student_name(name).by_exam(exam_id)
      else
        Subscriptions::ExamAttempt.none
      end

      all_records = attempts

      if search.present?
        if page_name.present? && page_name == 'exam'
          attempts = attempts.joins(subscription: [{ course: :lessons }, :student])
                             .where("CONCAT(users.first_name, ' ', users.last_name) ILIKE ?", "%#{search}%")
        elsif context[:current_user].class == Student
          attempts = attempts.joins(subscription: [{ course: :lessons }, :student])
                             .where("courses_courses.name ILIKE ? OR courses_lessons.title ILIKE ?", "%#{search}%", "%#{search}%")
        else
          attempts = attempts.joins(subscription: [{ course: :lessons }, :student])
                            .where("CONCAT(users.first_name, ' ', users.last_name) ILIKE ? OR courses_courses.name ILIKE ? OR courses_lessons.title ILIKE ?", "%#{search}%", "%#{search}%", "%#{search}%")
        end
      end

      if status.present?
        attempts = if status == "approved"
          attempts.approved_records
        elsif status == "rejected"
          attempts.disapproved_records
        elsif status == "initiated"
          attempts.corrected_records
        end
      end

      record_ids = attempts.map(&:id).uniq

      records = Subscriptions::ExamAttempt.includes(:exam_answers, :exam, subscription: [:course, :student]).where(id: record_ids).order('courses_lesson_exams.created_at DESC')
      {
        total_records: records.length,
        nodes: current_page ? records.page(current_page).per(25) : records,
        all_records: all_records,
        all_records_count: all_records.count
      }
    end    
  end
end
