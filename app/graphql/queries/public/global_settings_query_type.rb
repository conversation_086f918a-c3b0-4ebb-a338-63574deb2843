module Queries
  module Public
    module GlobalSettingsQueryType
      include ::Types::BaseInterface

      field :get_global_settings, ::Types::Objects::GlobalSettingsType, "Global Settings", null: false

      def get_global_settings
        Tenant.includes(licenses: :prices).find(context[:current_tenant].id)
      end

      field :get_custom_domains, [Types::Objects::CustomDomainType], null: false

      def get_custom_domains
        context[:current_tenant].custom_domains
      end
    end
  end
end