module Queries
  module Public::UsersQueryType
    include ::Types::BaseInterface

    field :check_email_exists, Types::Objects::Users::CheckEmailExistsType, null: false do
      argument :email, String, required: true
    end


    def check_email_exists(email: nil)
      without_tenant_protection do
        users = User.where(email: email)

        if users.present?
          if users.map(&:type).include?("Affiliate")
            {
              exists: true,
              role: 'Affiliate',
              message: "Este correo electrónico ya está registrado como afiliado. Utilice otro.",
              show_continue_option: false
            }
          elsif users.map(&:type).include?("SuperAdmin") || users.map(&:type).include?("Student")
            {
              exists: true,
              role: 'Other',
              message: "Este correo electrónico ya está registrado con otro rol. ¿Desea continuar como afiliado con este correo electrónico?",
              show_continue_option: true
            }
          else
            {
              exists: true,
              role: users.first.type,
              message: "Este correo electrónico ya está registrado con otro rol.",
              show_continue_option: false
            }
          end
        else
          {
            exists: false,
            role: nil,
            message: "Email is available.",
            show_continue_option: false
          }
        end
      end
    end
  end
end