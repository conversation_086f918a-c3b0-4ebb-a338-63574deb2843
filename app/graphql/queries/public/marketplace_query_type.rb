module Queries
  module Public::MarketplaceQueryType
    include ::Types::BaseInterface
    field :marketplace_courses, Types::CoursesConnection, 'Gets all visible marketplace courses of a subdomain', null: false do
      argument :name, String, required: false
      argument :category_ids, [Integer], required: false
      argument :price, String, required: false
      argument :rating, Float, required: false
    end

    def marketplace_courses(name: nil, category_ids: nil, price: nil, rating: nil)
      MultiTenantSupport::Current.tenant_account = nil
      MultiTenantSupport.turn_off_protection

      courses = Courses::Course.visible.on_marketplace.with_marketplace_categories(category_ids)
                  .with_name(name)
                  .with_price(price)
                  .order(total_rating: :desc)
                  .joins(:marketplace_categories).where.not(marketplace_categories: { type: 'Marketplaces::Subcategory' })
                  .order(Arel.sql(
                     "CASE
                       WHEN marketplace_categories.name = 'Negocios' THEN '1'
                       WHEN marketplace_categories.name = 'Marketing' THEN '2'
                     END"))

      if rating.present?
        courses = courses.where('total_rating >= ?', rating)
      end

      courses.select("courses_courses.id, courses_courses.name, courses_courses.created_at, is_visible, promo_picture_data, created_by_id, tenant_id")
    end 

    field :single_market_place_course, Types::Objects::Courses::PublicCourseType, 'Gets single visible marketplace courses of a subdomain', null: false do
      argument :course_id, ID, required: true
    end

    def single_market_place_course(course_id: nil)
      MultiTenantSupport::Current.tenant_account = nil
      MultiTenantSupport.turn_off_protection

      Courses::Course.visible.on_marketplace.find(course_id)
    end

    def with_rating(courses, rating)
      return courses if rating.nil?
      courses.select do |course|
        unless course.avg_rating.nil?
          course if course.reviews && course.avg_rating >= rating
        end  
      end
    end

    field :marketplace_categories_list, Types::Objects::Marketplaces::CourseCategoryType.connection_type, "A list of all tenants, by search string", null: false
  
    def marketplace_categories_list
      ::Marketplaces::Category.where(type: "Marketplaces::Category")
    end
  end
end