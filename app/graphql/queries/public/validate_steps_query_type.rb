module Queries
    module Public::ValidateStepsQueryType
      include ::Types::BaseInterface

      field :validate_super_admin, Types::Objects::SuperAdminType, null: false do
        argument :email, String, required: false
      end
    
      def validate_super_admin(email:)
        MultiTenantSupport.allow_read_across_tenant do
            super_admin = SuperAdmin.find_by(email:)
            raise GraphQL::ExecutionError.new 'SuperAdmin does not exist' if super_admin.nil?

            super_admin
        end
      end

      field :validate_academy, Types::Objects::TenantType, null: false do
        argument :academy, String, required: false
      end
    
      def validate_academy(academy:)
        MultiTenantSupport.without_current_tenant do
          MultiTenantSupport.allow_read_across_tenant do
            tenant = Tenant.find_by(subdomain: academy) 
            raise GraphQL::ExecutionError.new 'Tenant does not exist' if tenant.nil?

            tenant
          end
        end
      end
    end
end