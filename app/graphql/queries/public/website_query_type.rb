module Queries
  module Public::WebsiteQueryType
    include ::Types::BaseInterface

    field :get_website, Types::Objects::WebsiteType, "Gets a website by its subdomain", null: false do
      argument :id, ID, required: false
      argument :url, String, required: false
      argument :published, Bo<PERSON>an, required: false
      argument :affiliate_id, ID, required: false
    end

    def get_website(id: nil, url: nil, published: nil, affiliate_id: nil)
      websites = []
      if affiliate_id.present?
        without_tenant_protection do
          affiliate = ::Affiliate.find(affiliate_id)
          context[:affiliate] = affiliate
          websites = affiliate.websites.includes(body_sections: :margins)
        end
      else
        websites = context[:current_tenant].websites.includes(body_sections: [:margins, content: [{ website_button: [:button_styles, :styles]}] ])
      end
      websites = websites.published if published
      id.present? ? websites.find_by(id:) : websites.find_by(url:)
    end

    field :get_website_buttons, [Types::Objects::Websites::StyleType], null: false do
      argument :affiliate_id, ID, required: false
    end

    def get_website_buttons(affiliate_id: nil)
      if affiliate_id.present?
        without_tenant_protection do
          affiliate = ::Affiliate.find(affiliate_id)
          affiliate.website_buttons
        end
      else
        context[:current_tenant].website_buttons
      end
    end

    field :get_email_leads_forms, [Types::Objects::Websites::BodySectionEmailLeadsFormType], null: false do
      argument :affiliate_id, ID, required: false
    end

    def get_email_leads_forms(affiliate_id: nil)
      if affiliate_id.present?
        without_tenant_protection do
          Websites::BodySectionEmailLeadsForm.joins(body_section: :website).where(website: { user_id: affiliate_id })
        end
      else
        context[:current_tenant].email_leads_form_sections.joins(:body_section).includes(:lead_form_contacts)
      end
    end

    field :get_email_leads_form, Types::Objects::Websites::BodySectionEmailLeadsFormType, "Gets a email lead form by ID", null: false do
      argument :id, Integer, required: false
      argument :affiliate_id, ID, required: false
    end

    def get_email_leads_form(id:, affiliate_id: nil)
      if affiliate_id.present?
        without_tenant_protection do
          Websites::BodySectionEmailLeadsForm.joins(body_section: :website).where(website: { user_id: affiliate_id }).find_by(id:)
        end
      else
        context[:current_tenant].email_leads_form_sections.find_by(id:)
      end
    end

    field :get_categories, [Types::Objects::Courses::CategoryType], "Gets all categories of a subdomain", null: false

    def get_categories
      context[:current_tenant].categories
    end

    field :get_courses, Types::Objects::Courses::PublicCourseType.connection_type, "Gets all visible courses of a subdomain", null: false do
      argument :category_ids, [Integer], required: false
      argument :free_only, Boolean, required: false
    end

    def get_courses(category_ids: nil, free_only: nil)
      context[:current_tenant].courses.includes(:teachers, :student_reviews, { prices: :payment_methods }, { my_modules: :lessons }).visible.with_categories(category_ids).free?(free_only)
    end

    field :get_course_by_name, Types::Objects::Courses::PublicCourseType, "Get single course by name", null: false do
      argument :name, String, required: true
      argument :affiliate_id, ID, required: true
    end

    def get_course_by_name(name: nil, affiliate_id: nil)
      without_tenant_protection do
        context[:affiliate] = ::Affiliate.find(affiliate_id)
      end

      if affiliate_id.present?
        context[:affiliate].courses.on_affiliate_marketplace.find_by(name: name)
      else
        context[:current_tenant].courses.visible.find_by(name: name)
      end
    end

    field :get_communities, Types::Objects::Communities::CommunityType.connection_type, "Gets all visible courses of a subdomain", null: false do
      argument :free_only, Boolean, required: false
    end

    def get_communities(free_only: nil)
      context[:current_tenant].communities.visible
    end

    field :get_packs, Types::Objects::Courses::PackType.connection_type, "Gets all visible packs of a subdomain", null: false do
      argument :free_only, Boolean, required: false
    end

    def get_packs(free_only: nil)
      context[:current_tenant].packs.includes(courses: [:prices, { my_modules: :lessons }]).visible.free?(free_only)
    end

    field :get_public_lessons, Types::Objects::Courses::LessonType.connection_type, "Gets all public packs lessons", null: false do
      argument :course_id, Integer, required: true
    end

    def get_public_lessons(course_id: nil)
      context[:current_tenant].courses.find(course_id).lessons.where(is_public: true, content_type: "Courses::LessonVideo")
    end

    field :get_course_certificate_detail, Types::Objects::StudentCertificateType, null: false do
      argument :code, String, required: true
    end

    def get_course_certificate_detail(code:)
      StudentCertificate.find_by(certificate_qr_code: code)
    end

    field :get_email_leads_form_sections, Types::Objects::Websites::BodySectionEmailLeadsFormType.connection_type, null: false do
      argument :affiliate_id, ID, required: false
    end

    def get_email_leads_form_sections(affiliate_id: nil)
      if affiliate_id.present?
        without_tenant_protection do
          Websites::BodySectionEmailLeadsForm.joins(body_section: :website).where(website: { user_id: affiliate_id })
        end
      else
        context[:current_tenant].email_leads_form_sections.joins(body_section: :website).uniq
      end
    end

    field :get_email_leads_form_section, Types::Objects::Websites::BodySectionType, null: false do
      argument :id, Integer, required: false
      argument :affiliate_id, ID, required: false
    end

    def get_email_leads_form_section(id:, affiliate_id: nil)
      if affiliate_id.present?
        without_tenant_protection do
          lead_form = Websites::BodySectionEmailLeadsForm.find_by(id:)
        end
      else
        lead_form = context[:current_tenant].email_leads_form_sections.find_by(id:)
      end
      lead_form.body_section
    end
  end
end