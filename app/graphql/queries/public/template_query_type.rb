module Queries
  module Public
    module TemplateQueryType
      include Types::BaseInterface

      field :template_list, [Types::Objects::TemplateType], "A list of all templates", null: false
      field :get_template, Types::Objects::TemplateType, "A single template ", null: false do
        argument :id, ID, required: true
      end

      def template_list
        templates = []
        MultiTenantSupport.without_current_tenant do
          MultiTenantSupport.turn_off_protection do
            templates = Template.includes(website: :tenant).active
          end
        end
        templates
      end

      def get_template(id:)
        Template.find_by(id: id)
      end
    end
  end
end
