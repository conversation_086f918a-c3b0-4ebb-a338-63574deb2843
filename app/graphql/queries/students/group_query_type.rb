module Queries
  module Students
    module GroupQueryType
      include Types::BaseInterface

      field :available_courses, Types::Objects::Courses::CourseType.connection_type, "A list of all the current student user courses", null: false

      def available_courses
        context[:current_user].available_courses
      end

      field :available_packs, Types::Objects::Courses::PackType.connection_type, "A list of all the current student user packs", null: false

      def available_packs
        context[:current_user].available_packs
      end

      field :available_communities, Types::Objects::Communities::CommunityType.connection_type, "A list of all the current student user courses", null: false

      def available_communities
        context[:current_user].available_communities
      end
    end
  end
end
