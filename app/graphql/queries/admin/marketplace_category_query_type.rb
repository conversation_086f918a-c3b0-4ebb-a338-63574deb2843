module Queries
    module Admin
      module MarketplaceCategoryQueryType
        include Types::BaseInterface
  
        field :marketplace_categories_list, Types::Objects::Marketplaces::CourseCategoryType.connection_type, "A list of all tenants, by search string", null: false
  
        def marketplace_categories_list
          ::Marketplaces::Category.where(type: "Marketplaces::Category")
        end

        field :marketplace_subcategories_list, Types::Objects::Marketplaces::CourseCategoryType.connection_type, "A list of all tenants, by search string", null: false
  
        def marketplace_subcategories_list
          ::Marketplaces::Subcategory.all
        end
      end
    end
  end
