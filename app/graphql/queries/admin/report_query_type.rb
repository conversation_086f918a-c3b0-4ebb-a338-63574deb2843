module Queries
  module Admin
    module ReportQueryType
      include Types::BaseInterface

      field :generated_reports, Types::Objects::Reports::ReportType.connection_type, "A list of tags", null: false do
        argument :tab_id, Integer, required: false
      end

      def generated_reports(tab_id: nil)
        reports = context[:current_tenant].reports
        if tab_id.present?
          reports = reports.where(report_tab_id: tab_id)
        end
        reports.order('created_at desc')
      end

      field :report_tabs_list, Types::Objects::Reports::ReportTabType.connection_type, "A list of tags", null: false

      def report_tabs_list
        context[:current_tenant].report_tabs
      end

      field :all_videos_statistics, GraphQL::Types::JSON, "all videos analytics", null: false

      def all_videos_statistics
        BunnyCdn::BunnyApi.bunny_video_statistics(context[:current_tenant])
      end

      field :get_video_statistics, GraphQL::Types::JSON, "specific video analytics", null: false do
        argument :video_id, ID, required: true
      end

      def get_video_statistics(video_id: nil)
        lesson = context[:current_tenant].lessons.find(video_id)
        bunny_video_id = lesson.content&.content&.bunny_video_id
        BunnyCdn::BunnyApi.bunny_video_statistics(context[:current_tenant], video_id: bunny_video_id)
      end
    end
  end
end
