module Queries
  module Admin
    module CategoryQueryType
      include Types::BaseInterface

      field :admin_categories_list, Types::Objects::Courses::CategoryType.connection_type, "A list of all categories for the current admin user", null: false

      def admin_categories_list
        Courses::Category.all
      end

      field :get_category, Types::Objects::Courses::CategoryType, "A single category with course by ID", null: false do
        argument :id, Integer, required: true
      end

      def get_category(id:)
        ::Categories::Category.find(id)
      end
    end
  end
end