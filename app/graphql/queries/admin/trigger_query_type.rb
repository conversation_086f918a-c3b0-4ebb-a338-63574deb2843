module Queries
  module Admin
    module TriggerQueryType
      include Types::BaseInterface

      field :trigger_list, Types::Objects::Triggers::TriggerType.connection_type, null: false do
        argument :course_id, ID, required: true
      end

      def trigger_list(course_id: nil)
        course = Courses::Course.find(course_id)
        course.triggers
      end

      field :global_trigger_list, Types::Objects::Triggers::TriggerType.connection_type, null: false

      def global_trigger_list
        context[:current_tenant].triggers.where(course_id: nil)
      end
    end
  end
end
