module Queries
  module Admin
    module RefundQueryType
      include Types::BaseInterface

      field :refund_request_list, Types::Objects::Purchases::RefundRequestType.connection_type, "A list of all product purchase transactions for the current account", null: false

      def refund_request_list
        case context[:current_user]
        when ::Admin, SuperAdmin
          Purchases::RefundRequest.all
        when Student
          context[:current_user].refund_requests
        end
      end
    end
  end
end
