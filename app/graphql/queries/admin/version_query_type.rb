module Queries
    module Admin
      module VersionQueryType
        include Types::BaseInterface

        field :audit_version_list, [Types::Objects::VersionType], null: true do
          argument :user_type, String, required: false
        end

        def audit_version_list(name: nil, user_type: nil)
            return unless context[:current_tenant].feature_plan.audit_trail
            case context[:current_user]
            when SuperAdmin
              AuditVersion.includes(:user).all
            else
              AuditVersion.joins(:user).where(user: {id: context[:current_user].id})
                     .or(AuditVersion.joins(:user).where(user: {type: ['Student','Instructor']}))
            end.order(created_at: :desc).with_type(user_type)
        end
      end
    end
  end

