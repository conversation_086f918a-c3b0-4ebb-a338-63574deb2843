module Queries::Admin
  module GroupQueryType
    include Types::BaseInterface
    graphql_name "AdminGroupQuery"

    field :group_list, Types::Objects::GroupType.connection_type, "A list groups by search string", null: false do
      argument :search_string, String, "search string to get groups", required: false
    end

    def group_list(search_string: nil)
      Groups::Group.includes(:packs, :courses, :instructors, :students, :subscriptions).search_by_string(search_string)
    end

    field :get_group, Types::Objects::GroupType, "A group by ID", null: false do
      argument :id, Integer, "The ID of the group", required: false
    end

    def get_group(id:)
      Groups::Group.where(tenant: context[:current_tenant]).find(id)
    end
  end
end
