module Queries
  module Admin
    module PurchaseQueryType
      include Types::BaseInterface

      field :product_purchases_list, Types::Objects::Purchases::ProductPurchaseType.connection_type, "A list of all product purchase transactions for the current account", null: false

      def product_purchases_list
        Purchases::ProductPurchase.includes(:product).where(visibility: true)
      end

      field :get_product_purchase, Types::Objects::Purchases::ProductPurchaseType, 'Single product purchase', null: true do
        argument :product_id, ID, required: true
      end

      def get_product_purchase(product_id: nil)
        context[:current_user].product_purchases.find_by(product_id:, visibility: true)
      end
    end
  end
end