module Queries
  module Admin
    module AffiliateQueryType
      include Types::BaseInterface

      field :affiliate_list, Types::Interfaces::UserType.connection_type, null: false

      def affiliate_list
        without_tenant_protection do
          context[:current_tenant].affiliates.includes(:affiliates_tenants, :commissions, :affiliate_purchases).uniq
        end
      end

      field :affiliate_requests_list, Types::Objects::Affiliates::AffiliateRequestType.connection_type, null: false

      def affiliate_requests_list
        without_tenant_protection do
          context[:current_tenant].affiliates_courses.includes(:course).reorder(created_at: :desc)
        end
      end

      field :get_affiliate, Types::Interfaces::UserType, null: false, null: false do
        argument :id, ID, required: true
      end

      def get_affiliate(id:)
        without_tenant_protection do
          ::Affiliate.find(id)
        end
      end
    end
  end
end
