module Queries
  module Admin
    module Page<PERSON>ueryType
      include Types::BaseInterface

      field :pages_list, [Types::Objects::WebsiteType], "A list of all pages", null: false
      field :get_page, Types::Objects::WebsiteType, "A single page ", null: false do
        argument :id, ID, required: true
      end

      def pages_list
        if context[:current_user].affiliate? 
          context[:current_user].websites
        else
          context[:current_tenant].websites
        end
      end

      def get_page(id:)
        if context[:current_user].affiliate? 
          context[:current_user].websites.find_by(id: id)
        else
          context[:current_tenant].websites.find_by(id: id)
        end
      end
    end
  end
end
