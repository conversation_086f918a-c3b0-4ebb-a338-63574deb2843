module Queries
  module Admin
    module CourseQueryType
      include Types::BaseInterface

      field :admin_course_list, Types::Objects::Courses::CourseType.connection_type, "A list of all courses for the current admin user", null: false

      def admin_course_list
        context[:current_tenant].courses.includes([:subscriptions, :student_reviews, [licenses: :prices, prices: :payment_methods]])
      end

      field :lesson_list, Types::Objects::Courses::LessonType.connection_type, "A list of all lessons for the current admin user", null: false do
        argument :content_type, Types::Enums::Courses::LessonContentTypeType, "Type of lesson", required: false
        argument :title, String, "Filter and search lesson by title", required: false
      end

      def lesson_list(content_type: nil, title: nil)
        lessons = context[:current_tenant].lessons
                                .with_title(title)
                                .with_content_type(content_type)

        if content_type == "Courses::LessonExam"
          lessons = lessons.includes(:course, lesson_exams: :exam_attempts)
        end
        lessons
      end

      field :module_list, [Types::Objects::Courses::MyModuleType], "all Modules" 
      
      def module_list
        context[:current_tenant].my_modules 
      end

      field :get_module, Types::Objects::Courses::MyModuleType, "A single module by ID", null: false do
        argument :id, Integer, required: true
      end

      def get_module(id:)
        Courses::MyModule.where(tenant: context[:current_tenant]).find(id)
      end

      field :get_lesson, Types::Objects::Courses::LessonType, "A single lesson by ID", null: false do
        argument :id, Integer, "The lesson ID", required: true
        argument :subscription_id, Integer, "The subscription id, if it's a student", required: false
      end

      def get_lesson(id:, subscription_id: nil)
        case context[:current_user]
        when Student
          raise GraphQL::ExecutionError, "Subscription id is required" if subscription_id.nil?

          subscription = context[:current_user].subscriptions.includes(:course).find(subscription_id)
          context[:subscription] = subscription
          subscription.lessons.find(id)
        else
          Courses::Lesson.find(id)
        end
      end

      field :instructor_course_list, Types::Objects::Courses::CourseType.connection_type, null: false
      def instructor_course_list
        [*context[:current_user].courses, *context[:current_user].dictated_courses].uniq
      end

      field :get_course, Types::Objects::Courses::CourseType, "A single course by ID", null: false do
        argument :id, Integer, "The lesson ID", required: true
      end

      def get_course(id:)
        case context[:current_user]
        when Instructor
          course = context[:current_user].courses.find_by(id:)
          return context[:current_user].dictated_courses.find(id) if course.nil?
          course
        else
          context[:current_user].courses.includes(:teachers, :subscriptions, :marketplace_categories, my_modules: :lessons, prices: :payment_methods).find(id)
        end
      end

      field :pack_list, Types::Objects::Courses::PackType.connection_type, "A list of packs for the current user", null: false

      def pack_list
        context[:current_user].packs.includes(:courses, prices: :payment_methods)
      end

      field :get_pack, Types::Objects::Courses::PackType, "A single pack by ID", null: false do
        argument :id, Integer, "The pack ID", required: true
      end

      def get_pack(id:)
        context[:current_user].packs.find(id)
      end

      field :get_course_duration, Types::Objects::Courses::CourseDurationType, "Total duration of the video lesson content of course", null: false do
        argument :id, Integer, "The Course ID", required: true
      end

      def get_course_duration(id:)
        course = context[:current_tenant].courses.find_by(id:)
        total_minutes = course.video_lessons_length_in_minutes
        {
          hours: total_minutes / 60,
          minutes: total_minutes % 60
        }
      end
    end
  end
end