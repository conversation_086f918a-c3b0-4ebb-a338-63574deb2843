module Queries
    module Admin
      module CouponQueryType
        include Types::BaseInterface
  
        field :coupon_list, Types::Objects::Purchases::CouponType.connection_type, "A list of coupons", null: false do
          argument :search_string, String, "search string to get coupons", required: false
        end

        def coupon_list(search_string: nil)
          Purchases::Coupon.includes(:courses, :packs).search_by_string(search_string)
        end

        field :get_coupon, Types::Objects::Purchases::CouponType, null: false do
          argument :id, ID, 'find coupon by id', required: true
        end    
          
        def get_coupon(id:)
          Purchases::Coupon.find(id)
        end


        field :get_coupon_validate, Types::Objects::Purchases::CouponType, null: false do
          argument :coupon_code, String, 'find coupon by code', required: true
          argument :price_id, ID, 'find product price by id', required: true
        end    
          
        def get_coupon_validate(coupon_code:, price_id:)
          coupon = ::Purchases::Coupon.find_by(code: coupon_code)
          purchases_price = ::Purchases::Price.find(price_id)
          
          raise GraphQL::ExecutionError.new "Coupon not exist" if coupon.nil?
          raise GraphQL::ExecutionError.new "Invalid coupon for this product" unless coupon.products.include? purchases_price.product
          raise GraphQL::ExecutionError.new "Coupon not active" unless (coupon.status == 'active')

          coupon
        end

      end
    end
  end