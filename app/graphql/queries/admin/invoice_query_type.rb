module Queries
  module Admin
    module InvoiceQueryType
      include Types::BaseInterface

      field :invoices_list, Types::Objects::Invoices::InvoiceType.connection_type, null: false
      def invoices_list
          Invoices::Invoice.where(status: ['paid', 'open', 'failed']).order(payment_date: :desc)
      end

      # connected account payment histories
      field :payment_list, [Types::Objects::Purchases::StripeConnectHistoryType], null: false

      def payment_list
        account_id = Purchases::PaymentMethodStripeConnect.last&.account_id
        return [] unless account_id.present?

        Stripe.api_key = ENV.fetch('STRIPE_CONNECT_API_KEY', '')
        Stripe::Checkout::Session.list({}, { stripe_account: account_id })
      rescue => e
        []
      end
    end
  end
end
