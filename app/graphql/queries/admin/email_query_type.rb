module Queries
    module Admin
      module EmailQueryType
        include Types::BaseInterface

        field :signature_list, Types::Objects::Emails::SignatureType.connection_type, null: false
        def signature_list
            Emails::Signature.all
        end

        field :email_list, Types::Objects::Emails::EmailType.connection_type, null: false
        def email_list
            Emails::Email.all
        end

        field :template_list, Types::Objects::Emails::TemplateType.connection_type, null: false
        def template_list
            Emails::Template.all
        end

        field :automation_list, Types::Objects::Emails::AutomationType.connection_type, null: false
        def automation_list
          Emails::Automation.all.order(:is_default)
        end

        field :get_template, Types::Objects::Emails::TemplateType, null: false do
          argument :id, Integer, "ID", required: true
        end  

        def get_template(id:)
          Emails::Template.find(id)
        end

        field :get_email, Types::Objects::Emails::EmailType, null: false do
          argument :id, Integer, "ID", required: true
        end  

        def get_email(id:)
          Emails::Email.find(id)
        end
      end
    end
  end