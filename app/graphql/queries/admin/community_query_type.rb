module Queries
  module Admin
    module CommunityQueryType
      include Types::BaseInterface

      field :admin_community_list, Types::Objects::Communities::CommunityType.connection_type, "A list of all communities for the current admin user", null: false

      def admin_community_list
        context[:current_tenant].communities
      end

      field :instructor_community_list, Types::Objects::Communities::CommunityType.connection_type, null: false
      def instructor_community_list
        context[:current_user].dictated_communities
      end

      field :get_community, Types::Objects::Communities::CommunityType, "A single community by ID", null: false do
        argument :id, Integer, "The Community ID", required: true
      end

      def get_community(id:)
        case context[:current_user]
        when Instructor
          # community = context[:current_user].communities.find_by(id:)
          context[:current_user].dictated_communities.find(id)# if community.nil?
        else
          context[:current_user].communities.find(id)
        end
      end

      field :post_list_by_community, Types::Objects::CommunityPostPaginationListType, "A list of all posts for the community", null: false do
        argument :community_id, Integer, "The Community Post ID", required: true
        argument :page, Integer, "page number", required: false
      end

      def post_list_by_community(community_id:, page: nil)
        community = context[:current_tenant].communities.find(community_id)
        records = community.community_posts.order(created_at: :desc)
        {
          nodes: page ? records.page(page).per(5) : records  
        }
      end
    end
  end
end
