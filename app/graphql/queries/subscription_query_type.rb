module Queries
  module SubscriptionQueryType
    include Types::BaseInterface

    field :subscription_list, [Types::Objects::Subscriptions::SubscriptionType], "A list of all the current student user subscriptions", null: false

    def subscription_list
      context[:current_user].subscriptions
    end

    field :community_subscription_list, [Types::Objects::Subscriptions::SubscriptionType], "A list of all the current student user subscriptions", null: false

    def community_subscription_list
      context[:current_user].community_subscriptions
    end

    field :periodic_subscription_list, [Types::Objects::Subscriptions::SubscriptionType], null: false

    def periodic_subscription_list
      subscriptions = context[:current_user].subscriptions.where(type: 'Subscriptions::PeriodicSubscription')
    end

    field :community_periodic_subscription_list, [Types::Objects::Subscriptions::SubscriptionType], null: false

    def community_periodic_subscription_list
      subscriptions = context[:current_user].community_subscriptions.where(type: 'Subscriptions::PeriodicSubscription')
    end

    field :student_subscription_list, [Types::Objects::Subscriptions::SubscriptionType], "A list of all the given student user's subscriptions", null: false do
      argument :student_id, Integer, required: true
    end

    def student_subscription_list(student_id:)
      context[:current_tenant].students.find(student_id).subscriptions
    end


    field :instructor_subscription_list, [Types::Objects::Subscriptions::SubscriptionType], null: false 
    def instructor_subscription_list
      case context[:current_user]
      when Instructor
        Subscriptions::Subscription.where(course_id: context[:current_user].dictated_course_ids)
      else
        []
      end
    end

    field :get_subscription, Types::Objects::Subscriptions::SubscriptionType, "A single subscription", null: false do
      argument :id, Integer, required: true
    end

    def get_subscription(id:)
      subscription =
        case context[:current_user]
        when ::Admin, SuperAdmin
          context[:current_tenant].subscriptions.find(id)
        when ::Student
          context[:current_user].subscriptions.find(id)
        end
      context[:subscription] = subscription
      subscription
    end

    field :get_subscription_lesson, Types::Objects::Courses::LessonType, "A single lesson, in the context of a subscription", null: false do
      argument :subscription_id, Integer, required: true
      argument :lesson_id, Integer, required: true
    end

    def get_subscription_lesson(subscription_id, lesson_id)
      subscription = context[:current_tenant].subscriptions.find(subscription_id)
      context[:subscription] = subscription
      subscription.course.lessons.find(lesson_id)
    end
  end
end