module Queries
  module UserQueryType
    include Types::BaseInterface
    include UserFilterable

    field :current_user, Types::Interfaces::UserType, "The current user", null: false

    def current_user
      context[:current_user]
    end

    field :get_user, Types::Interfaces::UserType, "A single user by ID", null: false do
      argument :id, Integer, "The user ID", required: true
    end

    def get_user(id:)
      context[:current_tenant].users.find(id)
    end

    field :get_user_by_email, Types::Interfaces::UserType, "A single user by ID", null: false do
      argument :email, String, "The user's email", required: true
    end

    def get_user_by_email(email:)
      context[:current_tenant].users.find_by!(email:)
    end

    field :get_users, Types::Interfaces::UserType.connection_type, "A list users by search string", null: false do
      argument :search_string, String, "search string to get users", required: false
      argument :filters, Types::Inputs::GetUsersInputType, required: false
    end

    def get_users(search_string: nil, filters: nil)
      case context[:current_user]
      when ::Admin, SuperAdmin
        users = context[:current_tenant].users.where.not(type: 'SuperAdmin').search_by_string(search_string)
      when Instructor
        # logica solo para el instructor (por ahora es la manera de hacerlo)
        students = Subscriptions::Subscription.students_by_course_ids(context[:current_user].dictated_course_ids)
        student_ids = students.map(&:id) + context[:current_user].students.map(&:id)
        users = Student.where(id: student_ids)
      when Student
        raise GraphQL::ExecutionError, "This user type can't access this query"
      end
      users = apply_filters(users, filters) if filters.present?
      users = User.where(id: users.map(&:id)).where.not(type: 'Affiliate')
      users.includes(:tenant, :courses, :groups, :tags)
    end

    field :get_teachers, Types::Interfaces::UserType.connection_type, null: false

    def get_teachers
      case context[:current_user]
      when Student
        raise GraphQL::ExecutionError, "This user type can't access this query"
      else
        context[:current_tenant].users.where.not(type: 'Student')        
      end
    end

    field :lead_list, Types::Objects::StudentType.connection_type, "A list of leads", null: false

    def lead_list
      case context[:current_user]
      when ::Admin, SuperAdmin
        context[:current_tenant].students.leads.includes(:tags, :courses, :groups)
      else
        raise GraphQL::ExecutionError, "This user type can't access this query"
      end
    end

    field :calendar_events, Types::Objects::Users::EventType.connection_type, null: false do
      argument :from, GraphQL::Types::ISO8601Date, required: false
    end

    def calendar_events(from: nil)
      from ||= 1.week.ago
      return [] unless context[:current_user].is_a? Student

      context[:current_user].lessons.events.select('subscriptions_subscriptions.id AS subscription_id, courses_lessons.*').map do |lesson|
        # These are all the lessons of type webinar or vidocall
        {
          key: lesson.id.to_s,
          datetime: lesson.content.date,
          label: lesson.title,
          extras: {
            subscription_id: lesson.subscription_id,
            lesson_id: lesson.id,
            lesson_title: lesson.title,
            my_module_id: lesson.my_module_id,
          }
        }
      end
    end

    field :get_not_enrolled_users_to_community, Types::Interfaces::UserType.connection_type, null: false do
      argument :community_id, Integer, required: true
    end

    def get_not_enrolled_users_to_community(community_id:)
      context[:current_tenant].students.not_in_community(community_id)
    end

    field :get_tags, Types::Objects::TagType.connection_type, "A list of tags", null: false

    def get_tags
      context[:current_tenant].tags
    end

    field :super_admin_tenants, Types::Objects::TenantType.connection_type, "A list of super admin academies", null: true

    def super_admin_tenants
      Tenant.joins(:super_admin).where(super_admin: { email: context[:current_user].email }).uniq
    end

    field :student_tenants, Types::Objects::TenantType.connection_type, "A list of student academies", null: true

    def student_tenants
      Tenant.joins(:students).where(students: { email: context[:current_user].email })
    end

    field :fetch_affiliate_user, Types::Interfaces::UserType, null: true

    def fetch_affiliate_user
      ::Affiliate.find_by(email: context[:current_user].email)
    end
  end
end
