module Queries
  module SurveyQueryType
    include Types::BaseInterface

    field :get_survey_list, Types::Objects::Courses::LessonSurveyType.connection_type, null: false do
      argument :course_id, Integer, required: false
    end

    def get_survey_list(course_id: nil)
      surveys = context[:current_tenant].surveys
      if course_id.present?
        surveys = surveys.joins(:course).where(course: { id: course_id })
      end
      surveys
    end

    field :get_survey_response, Types::Objects::Subscriptions::SurveyResponseType, "A single survey response by ID", null: false do
      argument :id, Integer, "The survey response by ID", required: true
    end

    def get_survey_response(id:)
      context[:current_tenant].survey_responses.find(id)
    end

    field :get_survey_response_list, Types::Objects::Subscriptions::SurveyResponseType.connection_type, "A list survey responses", null: false do
      argument :survey_id, Integer, "Filter by survey ID to get survey responses", required: false
      argument :course_id, Integer, required: false
    end

    def get_survey_response_list(survey_id: nil, course_id: nil)
      survey_responses = context[:current_tenant].survey_responses

      if survey_id.present?
        survey_responses = survey_responses.by_survey(survey_id)
      end

      if course_id.present?
        survey_responses = survey_responses.joins(survey: :course).where(course: { id: course_id })
      end
      survey_responses
    end    
  end
end
