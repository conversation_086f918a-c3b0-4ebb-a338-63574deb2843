module Queries
  module MediaQueryType
    include Types::BaseInterface

    field :lesson_content_items, Types::Objects::Media::LessonContentType.connection_type, "Todos los contenidos de lecciones subidos", null: false do
      argument :search, String, "Palabra para buscar el contenido por nombre", required: false
      argument :media_types, [Types::Enums::Media::LessonContentFileType], "Los tipos de media que se ban a buscar", required: false
    end

    def lesson_content_items(search: nil, media_types: nil)
      Media::LessonContent.search(search).filter_by_types(media_types)
    end
  end
end