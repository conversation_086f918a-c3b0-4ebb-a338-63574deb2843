module Queries
  module Affiliate
    module AffiliateCourseQueryType
      include Types::BaseInterface

      field :affiliate_course_list, Types::Objects::Courses::CourseType.connection_type, null: false

      def affiliate_course_list
        context[:current_user].courses.includes(:affiliates_links, :affiliates_courses, :affiliate_setting, :teachers, :student_reviews, :my_modules, { prices: :payment_methods}).uniq
      end

      field :affiliate_single_course, Types::Objects::Courses::CourseType, "A single affiliate course by ID", null: false do
        argument :id, Integer, "The course ID", required: true
      end

      def affiliate_single_course(id:)
        Courses::Course.on_affiliate_marketplace.find(id)
      end

      field :global_affiliate_course_list, Types::Objects::Courses::CourseType.connection_type, null: false

      def global_affiliate_course_list
        Courses::Course.on_affiliate_marketplace.includes(:affiliates_links, :affiliates_courses, :affiliate_setting, :teachers, :student_reviews, :my_modules, { prices: :payment_methods})
      end

      field :new_affiliate_courses, Types::Objects::Courses::CourseType.connection_type, null: false

      def new_affiliate_courses
        Courses::Course.on_affiliate_marketplace - context[:current_user].courses
      end
    end
  end
end
