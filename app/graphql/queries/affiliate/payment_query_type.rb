module Queries
  module Affiliate
    module PaymentQueryType
      include Types::BaseInterface

      field :affiliate_payment_methods, [Types::Interfaces::Affiliates::PaymentsMethodType], null: false

      def affiliate_payment_methods
        context[:current_user].payment_methods
      end

      field :affiliate_product_purchases_list, Types::Objects::Purchases::ProductPurchaseType.connection_type, "A list of all product purchase transactions for the current affiliate", null: false

      def affiliate_product_purchases_list
        context[:current_user].product_purchases
      end

      field :affiliate_refund_request_list, Types::Objects::Purchases::RefundRequestType.connection_type, "A list of all product purchase transactions for the current account", null: false

      def affiliate_refund_request_list
        context[:current_user].refund_requests
      end

      field :get_affiliate_product_purchase, Types::Objects::Purchases::ProductPurchaseType, 'Single product purchase', null: true do
        argument :product_id, ID, required: true
      end

      def get_affiliate_product_purchase(product_id: nil)
        context[:current_user].product_purchases.find_by(product_id:, visibility: true)
      end
    end
  end
end
