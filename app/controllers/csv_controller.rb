class CsvController < ActionController::API
    include ActionController::MimeResponds
    
    def index
        t = Tenant.find_by(subdomain: params["subdomain"])

        MultiTenantSupport.under_tenant t do
         respond_to do |format|
            format.html
            format.csv { send_data AuditVersion.to_csv, filename: "audit-#{DateTime.now.strftime("%d%m%Y%H%M")}.csv"}
         end
        end
    end
end
  