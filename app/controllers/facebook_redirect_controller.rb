class FacebookRedirectController < ActionController::API
  def execute
    code = params[:code]
    values = params[:state].split(",")
    if values.include?('SuperAdmin')
      hostname = values[0]
      uri = URI("https://graph.facebook.com/v20.0/oauth/access_token")
      uri.query = URI.encode_www_form({
        client_id: '1705172716938191',
        redirect_uri: "#{request.base_url}/facebook_redirect",
        client_secret: '3645f8da004da5aeb68d8b84c0f346fc',
        code: code
      })

      # Make the HTTP GET request to exchange the code for an access token
      response = Net::HTTP.get_response(uri)
      token_json = JSON.parse(response.body)
      access_token = token_json['access_token']

      uri = URI("https://graph.facebook.com/me?access_token=#{access_token}&fields=id,email")
      response = Net::HTTP.get(uri)
      facebook_data = JSON.parse(response)
      MultiTenantSupport.turn_off_protection
      @user = SuperAdmin.find_by(email: facebook_data["email"])

      if @user.present?
        unless Rails.env.development?
          hostname = hostname.gsub('onboarding', @user.tenant.subdomain)
        end
        MultiTenantSupport.under_tenant @user.tenant do
          sign_in(@user)
          login_token = @user.create_new_auth_token

          # Build the query parameters from login_token hash
          query_params = URI.encode_www_form(
            "authHeaders[access-token]" => login_token["access-token"],
            "authHeaders[token-type]" => login_token["token-type"],
            "authHeaders[client]" => login_token["client"],
            "authHeaders[expiry]" => login_token["expiry"],
            "authHeaders[uid]" => login_token["uid"],
            "authHeaders[Authorization]" => login_token["Authorization"]
          )
          redirect_to("#{hostname}/external-login?#{query_params}",  allow_other_host: true)
        end
      end
    else
      hostname = values[0]
      path = values[1]
      redirect_to("http://#{hostname}/callbacks/facebook-login?path=#{path}&code=#{code}",  allow_other_host: true)
    end
  end
end
