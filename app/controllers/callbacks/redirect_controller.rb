class Callbacks::RedirectController < ActionController::API
  def paypal_redirect
    hostname = params[:state]
    code = params[:code]
    redirect_to("https://#{hostname}/callbacks/paypal-oauth-login?code=#{code}",  allow_other_host: true)
  end

  def mercado_pago_redirect
    hostname = params[:state]
    code = params[:code]
    redirect_to("https://#{hostname}/callbacks/mercadopago-oauth-login?code=#{code}",  allow_other_host: true)
  end
end
