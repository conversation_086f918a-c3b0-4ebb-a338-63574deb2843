class Callbacks::MercadoPagoController < ApplicationController
  def callback
    code = params[:code]
    code_verifier = params[:code_verifier]

    if code.blank? || code_verifier.blank?
      return render plain: "Missing code or code verifier", status: :bad_request
    end

    uri = URI("https://api.mercadopago.com/oauth/token")
    response = Net::HTTP.post_form(uri, {
      grant_type: "authorization_code",
      client_id: ENV['MERCADOPAGO_OAUTH_CLIENT_ID'],
      client_secret: ENV['MERCADOPAGO_OAUTH_SECRET_KEY'],
      code: code,
      code_verifier: code_verifier,
      redirect_uri: "#{BASE_URL}/mercado_pago_redirect"
    })

    result = JSON.parse(response.body)

    if response.code == "200"
      payment_method = current_tenant.payment_methods.find_or_initialize_by(type: 'Purchases::PaymentMethodMercadoPago', name: "<PERSON>rcadoPago")

      payment_method.update!(
        oauth_access_token: result["access_token"],
        oauth_public_key: result["public_key"],
        refresh_token: result["refresh_token"],
        user_id: result["user_id"],
        expires_at: Time.current + result["expires_in"].to_i.seconds,
        one_click_enabled: true
      )

      res = payment_method.get_user_info
      if res.code == 200
        payment_method.country = res["country_id"].downcase 
        payment_method.save
      end
      render json: { success: true }
    else
      render json: { error:  "OAuth error: #{result["message"]}", description: result["message"] }
    end
  end
end
