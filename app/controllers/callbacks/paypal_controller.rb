
class Callbacks::PaypalController < ApplicationController
  def callback
    code = params[:code]

    if code.blank?
      return render plain: "Missing code", status: :bad_request
    end

    token_url = URI("#{Rails.configuration.pay_pal_api_base}oauth2/token")

    req = Net::HTTP::Post.new(token_url)
    req.basic_auth(ENV["PAYPAL_OAUTH_CLIENT_ID"], ENV["PAYPAL_OAUTH_SECRET_KEY"])
    req.set_form_data({
      grant_type: "authorization_code",
      code: code,
      redirect_uri: "#{BASE_URL}/paypal_redirect"
    })

    http = Net::HTTP.new(token_url.hostname, token_url.port)
    http.use_ssl = true
    response = http.request(req)

    result = JSON.parse(response.body)

    if response.code == "200"
      payment_method = current_tenant.payment_methods.find_or_initialize_by(type: 'Purchases::PaymentMethodPayPal', name: "PayPal")
      payment_method.update!(
        access_token: result["access_token"],
        refresh_token: result["refresh_token"],
        expires_at: Time.current + result["expires_in"].to_i.seconds,
        one_click_enabled: true
      )

      payment_method.merchant_id = payment_method.request_merchant_id
      payment_method.save!

      response = payment_method.request_merchant_access_token
      if response.success?
        payment_method.access_token = response['access_token']
        payment_method.expires_at = Time.current + response['expires_in'].to_i.seconds
        payment_method.save!
      end
      render json: { success: true }
    else
      render json: { error: result["error"], description: result["error_description"] }
    end
  end
end
