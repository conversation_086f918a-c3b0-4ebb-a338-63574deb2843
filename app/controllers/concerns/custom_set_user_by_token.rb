module CustomSetUserByToken
  extend ActiveSupport::Concern
  include DeviseTokenAuth::Concerns::SetUserByToken
  include Multi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def set_user_by_token(mapping = nil)
    # determine target authentication class
    rc = resource_class(mapping)

    # no default user defined
    return unless rc

    # gets the headers names, which was set in the initialize file
    uid_name = DeviseTokenAuth.headers_names[:'uid']
    access_token_name = DeviseTokenAuth.headers_names[:'access-token']
    client_name = DeviseTokenAuth.headers_names[:'client']

    # gets values from cookie if configured and present
    parsed_auth_cookie = {}
    if DeviseTokenAuth.cookie_enabled
      auth_cookie = request.cookies[DeviseTokenAuth.cookie_name]
      if auth_cookie.present?
        parsed_auth_cookie = JSON.parse(auth_cookie)
      end
    end

    # parse header for values necessary for authentication
    uid              = request.headers[uid_name] || params[uid_name] || parsed_auth_cookie[uid_name]
    @token           = DeviseTokenAuth::TokenFactory.new unless @token
    @token.token     ||= request.headers[access_token_name] || params[access_token_name] || parsed_auth_cookie[access_token_name]
    @token.client ||= request.headers[client_name] || params[client_name] || parsed_auth_cookie[client_name]

    # client isn't required, set to 'default' if absent
    @token.client ||= 'default'

    # check for an existing user, authenticated via warden/devise, if enabled
    if DeviseTokenAuth.enable_standard_devise_support
      devise_warden_user = warden.user(mapping)
      if devise_warden_user && devise_warden_user.tokens[@token.client].nil?
        @used_auth_by_token = false
        @resource = devise_warden_user
        # REVIEW: The following line _should_ be safe to remove;
        #  the generated token does not get used anywhere.
        # @resource.create_new_auth_token
      end
    end

    # user has already been found and authenticated
    return @resource if @resource && @resource.is_a?(rc)

    # ensure we clear the client
    unless @token.present?
      @token.client = nil
      return
    end

    user, users = nil
    without_tenant_protection do
      if current_tenant.present?
        user = uid && rc.dta_find_by(uid: uid, tenant_id: current_tenant.id)
      else
        users = uid && rc.where(uid: uid)
        user = users.first
      end
      if user.present? && current_tenant.present?
        unless user.tenant_id == current_tenant.id || user.tenants.exists?(id: current_tenant.id)
          user = nil
        end
      end
    end
    scope = rc.to_s.underscore.to_sym

    if users.present? && users.count > 1
      user = users.find { |u| u.valid_token?(@token.token, @token.client) }
    end

    if user && user.valid_token?(@token.token, @token.client)
      # sign_in with bypass: true will be deprecated in the next version of Devise
      if respond_to?(:bypass_sign_in) && DeviseTokenAuth.bypass_sign_in
        bypass_sign_in(user, scope: scope)
      else
        sign_in(scope, user, store: false, event: :fetch, bypass: DeviseTokenAuth.bypass_sign_in)
      end
      return @resource = user
    else
      # zero all values previously set values
      @token.client = nil
      return @resource = nil
    end
  end

  def update_auth_header
    without_tenant_protection do
      super
    end
  rescue ActiveRecord::RecordNotFound
    raise DeviseTokenAuth::Errors::InvalidModel, "Resource not found due to multi-tenancy constraints"
  end

end