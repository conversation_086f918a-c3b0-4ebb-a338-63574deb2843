class Network::RestWebhooksController < ApiKeyController
  before_action :set_network_rest_webhook, only: %i[ show update destroy ]

  # GET /network/rest_webhooks
  def index
    @network_rest_webhooks = Network::RestWebhook.all

    render json: @network_rest_webhooks
  end

  # GET /network/rest_webhooks/1
  def show
    render json: @network_rest_webhook
  end

  # POST /network/rest_webhooks
  def create
    @network_rest_webhook = Network::RestWebhook.new(network_rest_webhook_params)

    if @network_rest_webhook.save
      render json: @network_rest_webhook, status: :created, location: @network_rest_webhook
    else
      render json: @network_rest_webhook.errors, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /network/rest_webhooks/1
  def update
    if @network_rest_webhook.update(network_rest_webhook_params)
      render json: @network_rest_webhook
    else
      render json: @network_rest_webhook.errors, status: :unprocessable_entity
    end
  end

  # DELETE /network/rest_webhooks/1
  def destroy
    @network_rest_webhook.destroy
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_network_rest_webhook
      @network_rest_webhook = Network::RestWebhook.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def network_rest_webhook_params
      params.require(:network_rest_webhook).permit(:tenant_id, :target, :state, events: [])
    end
end
