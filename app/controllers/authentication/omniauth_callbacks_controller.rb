class Authentication::OmniauthCallbacksController < DeviseTokenAuth::RegistrationsController
    def redirect_callbacks
      type = params[:type].present? && params[:type] == 'SuperAdmin' ? SuperAdmin : Student
      if params[:provider] == 'facebook'
        code = params[:code]

        uri = URI("https://graph.facebook.com/v20.0/oauth/access_token")
        uri.query = URI.encode_www_form({
          client_id: '1705172716938191',
          redirect_uri: "#{request.base_url}/facebook_redirect",
          client_secret: '3645f8da004da5aeb68d8b84c0f346fc',
          code: code
        })

        response = Net::HTTP.get_response(uri)
        token_json = JSON.parse(response.body)
        access_token = token_json['access_token']

        uri = URI("https://graph.facebook.com/me?access_token=#{access_token}&fields=id,email,first_name,last_name,picture")
        response = Net::HTTP.get(uri)
        facebook_data = JSON.parse(response)
        provider_data = OpenStruct.new(
          provider: 'facebook',
          uid: facebook_data["id"],
          info: OpenStruct.new(
            email: facebook_data["email"],
            first_name: facebook_data["first_name"],
            last_name: facebook_data["last_name"],
            image: facebook_data['picture']['data']['url']
          )
        )
        @user = User.from_omniauth(provider_data, type)
      else
        @user = User.from_omniauth(request.env['omniauth.auth'], type)
      end
      if @user.persisted?
        return if check_user_limit_restrictions

        sign_in(@user)
        login_token = @user.create_new_auth_token
        render json: {
                      status: 'SUCCESS',
                      message: "user was successfully logged in through #{params[:provider]}",
                      headers: login_token
                      },
                status: :created
      else
        render json: {
                      status: 'FAILURE',
                      message: "There was a problem signing you in through #{params[:provider]}",
                      errors: @user.errors
                      },
                status: :unprocessable_entity
      end
    end

    private

    def check_user_limit_restrictions
      tenant = @user.tenant
      if @user&.type == 'Admin' && tenant.admins.count > current_tenant.feature_plan.user_limits['admins']
        render json: {
          success: false,
          type: :plan_limit_exceeded,
          error_type: :admin_plan_limit_exceeded,
        }, status: :forbidden
        return true
      end
  
      if  @user&.type == 'Instructor' && current_tenant.instructors.count > current_tenant.feature_plan.user_limits['instructors']
        render json: {
          success: false,
          type: :plan_limit_exceeded,
          error_type: :instructor_plan_limit_exceeded,
        }, status: :forbidden
        return true
      end
    rescue => e
      false
    end
end
    