class Authentication::<PERSON><PERSON><PERSON>roller < DeviseTokenAuth::SessionsController
    # before_action :check_user_limit, only: :create
  def create
    MultiTenantSupport.without_current_tenant do
      super do |resource|
        redirect_url = ''
        if resource.tenants.size == 1 && resource.type != 'Affiliate' && !resource.affiliate_user
          subdomain = resource.tenants.first&.subdomain
          domain, protocol = case Rails.env
            when 'development'
              ['sabionetlocal.com:8080', 'http']
            when 'staging'
              ['sabiolms.com', 'https']
            when 'production'
              ['sabionet.com', 'http']
            end
          redirect_url = "#{protocol}://#{subdomain}.#{domain}/external-login"
        end

        check_student_restriction(resource)
        if @verification_required
          resource.tokens.delete(resource.tokens.keys.last)
          resource.save!
          return render json: { success: false, message: "Verification required. Check your email.", requires_verification: true }
        end

        return render json: {
          success: true,
          message: 'Signed in successfully!',
          data: resource,
          user_type: resource.type,
          tenants: resource.tenants,
          redirect_url: redirect_url
        }
      end
    end
  end

  def destroy
    session = current_user&.user_sessions&.find_by(sign_in_token: request.headers["client"])
    session.destroy if session.present?
    super
  end

  def find_resource(field, value)
    without_tenant_protection do
      if current_tenant.present?
        @resource = resource_class.dta_find_by(field => value, 'provider' => provider, tenant_id: current_tenant.id)
      else
        resources = resource_class.where(field => value, 'provider' => provider)
        @resource = if resources.count > 1
          resources.find_by(type: ['SuperAdmin', 'Student'])
        else
          resources.first
        end
      end
    end

    @resource.current_academy = @resource.tenant if @resource.present?
    if (request.origin.include?('centralized') && ['SuperAdmin', 'Student', 'Affiliate'].include?(@resource&.type)) || @resource&.type == 'Affiliate'
      @resource
    elsif current_tenant.present? && @resource.present? && (@resource.tenant_id == current_tenant.id || @resource.tenants.map(&:id).include?(current_tenant.id))      
      @resource
    end
  end

  private

  def check_user_limit
    return nil if current_tenant.nil?

    if field = (resource_params.keys.map(&:to_sym) & resource_class.authentication_keys).first
      q_value = get_case_insensitive_field_from_resource_params(field)

      @resource = find_resource(field, q_value)
    end

    if @resource&.type == 'Admin' && current_tenant.admins.count > current_tenant.feature_plan.user_limits['admins']
      return render json: {
        success: false,
        type: :plan_limit_exceeded,
        error_type: :admin_plan_limit_exceeded,
      }, status: :forbidden
    end

    if @resource&.type == 'Instructor' && current_tenant.instructors.count > current_tenant.feature_plan.user_limits['instructors']
      return render json: {
        success: false,
        type: :plan_limit_exceeded,
        error_type: :instructor_plan_limit_exceeded,
      }, status: :forbidden
    end
  rescue => e
  end

  def check_student_restriction(resource)
    return if resource.nil? || resource.type != 'Student'

    tenant = resource.tenants.first

    return unless tenant&.restrict_sessions?

    client_id = resource.tokens.keys.last

    if resource.exceeds_session_limit? && tenant.requires_verification
      resource.send_verification_email
      @verification_required = true
      return
    end

    if resource.exceeds_session_limit?
      resource.remove_old_session
    end

    resource.user_sessions.create!(
      ip_address: request.remote_ip,
      user_agent:  request.user_agent,
      expires_at: 30.days.from_now,
      sign_in_token: client_id
    )
  end
end
