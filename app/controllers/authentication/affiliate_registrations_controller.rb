class Authentication::AffiliateRegistrationsController < DeviseTokenAuth::RegistrationsController

  protected

  def build_resource
    without_tenant_protection do
      @resource = Affiliate.new(**sign_up_params)
      @resource.provider   = provider
    end

    if resource_class.case_insensitive_keys.include?(:email)
      @resource.email = sign_up_params[:email].try(:downcase)
    else
      @resource.email = sign_up_params[:email]
    end

    if params[:country].present?
      @resource.country = params[:country]
    end
    @resource.ip_address = request.remote_ip
  end
end
