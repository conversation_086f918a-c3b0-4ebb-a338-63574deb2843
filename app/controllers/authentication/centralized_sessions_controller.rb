class Authentication::CentralizedSessionsController < DeviseTokenAuth::SessionsControll<PERSON>
  def create
    MultiTenantSupport.turn_off_protection
    super do |resource|
      # check_student_restriction(resource)

      check_centralized_verification

      redirect_url = get_redirect_url(resource)

      if @verification_failed
        resource.tokens.delete(resource.tokens.keys.last)
        resource.save!
        return render json: {
          success: false,
          message: 'Verification Failed',
          data: '',
          verification_failed: true
        }
      end 
      if @verification_required || @centralized_verification_required
        resource.tokens.delete(resource.tokens.keys.last)
        resource.save!
        return render json: { success: false, message: "Verification required. Check your email.", requires_verification: @verification_required, centralized_verification_required: @centralized_verification_required }
      end

      return render json: {
        success: true,
        message: 'Signed in successfully!',
        data: resource,
        user_type: resource.type,
        redirect_url: redirect_url
      }
    end
  end

  def find_resource(field, value)
    @resources = User.where(field => value, type: ['SuperAdmin', 'Student', 'Affiliate'])

    if @resources.count > 1
      @resource = @resources.find { |user| user.valid_password?(params[:password]) }
    else
      @resource = @resources.first
    end
    @resource
  end

  private

  def get_redirect_url(resource)
    return '' if (resource.type != 'Affiliate' && resource.tenants.many?) || @resources&.many?

    domain, protocol = case Rails.env
                       when 'development' then ['sabionetlocal.com:8080', 'http']
                       when 'staging' then ['sabiolms.com', 'https']
                       when 'production' then ['sabionet.com', 'https']
                       end

    if resource.type == 'Affiliate'
      redirect_url = "#{protocol}://centralized.#{domain}/affiliate/external-login"
    else
      redirect_url = "#{protocol}://#{resource.tenant.subdomain}.#{domain}/external-login"
    end
    redirect_url
  end
  

  def check_centralized_verification
    return if @resource.nil?

    if @resources.count > 1
      if params[:otp].present?
        if @resource.login_verification_code === params[:otp]
          @verification_failed = false
        else
          @verification_failed = true
        end
      else
        @centralized_verification_required = true
        @resource.send_verification_email
      end
    end
  end

  def check_student_restriction(resource)
    return if resource.nil? || resource.type != 'Student' 

    tenant = resource.tenants.first

    return unless tenant&.restrict_sessions?

    client_id = resource.tokens.keys.last

    if resource.exceeds_session_limit? && tenant.requires_verification
      resource.send_verification_email
      @verification_required = true
      return
    end

    if resource.exceeds_session_limit?
      resource.remove_old_session
    end

    resource.user_sessions.create!(
      ip_address: request.remote_ip,
      user_agent:  request.user_agent,
      expires_at: 30.days.from_now,
      sign_in_token: client_id
    )
  end
end
