class GoogleRedirectController < ActionController::API
  def execute
    values = params[:state].split(",")
    code = params[:code]
    if values.include?('SuperAdmin')
      hostname = values[0]
      path = values[1]

      client_id = ENV['GOOGLE_CLIENT_ID'] #'757936244401-av6ojk29pvi0b0u5diujsnhpav2ocd13.apps.googleusercontent.com'
      client_secret = ENV['GOOGLE_CLIENT_SECRET'] # '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'
      redirect_uri = "#{request.base_url}/google_redirect"

      uri = URI.parse('https://oauth2.googleapis.com/token')
      response = Net::HTTP.post_form(uri, {
        'code' => code,
        'client_id' => client_id,
        'client_secret' => client_secret,
        'redirect_uri' => redirect_uri,
        'grant_type' => 'authorization_code'
      })

      token_data = JSON.parse(response.body)
      access_token = token_data['access_token']
      id_token = token_data['id_token']

      decoded_token = JWT.decode(id_token, nil, false).first
      email = decoded_token['email']
      MultiTenantSupport.turn_off_protection
      @user = SuperAdmin.find_by(email: email)

      if @user.present?
        unless Rails.env.development?
          hostname = hostname.gsub('onboarding', @user.tenant.subdomain)
        end
        MultiTenantSupport.under_tenant @user.tenant do
          sign_in(@user)
          login_token = @user.create_new_auth_token

          # Build the query parameters from login_token hash
          query_params = URI.encode_www_form(
            "authHeaders[access-token]" => login_token["access-token"],
            "authHeaders[token-type]" => login_token["token-type"],
            "authHeaders[client]" => login_token["client"],
            "authHeaders[expiry]" => login_token["expiry"],
            "authHeaders[uid]" => login_token["uid"],
            "authHeaders[Authorization]" => login_token["Authorization"]
          )
          redirect_to("#{hostname}/external-login?#{query_params}",  allow_other_host: true)
        end
      end
    else
      hostname = values[0]
      path = values[1]
      redirect_to("http://#{hostname}/callbacks/social-login?path=#{path}&code=#{code}",  allow_other_host: true)
    end
  end
end
