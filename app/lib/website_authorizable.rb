module WebsiteAuthorizable
  def fetch_website(id)
    website = if context[:current_user].type == 'Affiliate'
      context[:current_user].websites.find_by(id:)
    else
      context[:current_tenant].websites.find_by(id:)
    end
    raise 'Website Page not found' unless website.present?
    website
  end

  def validate_website_access(id)
    fetch_website(id)
  end

  def fetch_website_section(id)
    section = if context[:current_user].type == 'Affiliate'
      context[:current_user].body_sections.find_by(id:)
    else
      context[:current_tenant].body_sections.find_by(id:)
    end
    raise 'Website Section not found' unless section.present?
    section
  end

  def fetch_website_button(id)
    button = if context[:current_user].type == 'Affiliate'
      context[:current_user].website_buttons.find_by(id:)
    else
      ::Websites::Button.find_by(id:)
    end
    raise 'Website Button not found' unless button.present?
    button
  end

  def fetch_website_section_content(id, content_type_class)
    content = if context[:current_user]&.type == 'Affiliate'
      content_type_class.joins(body_section: :website).where(website: { user_id: context[:current_user].id }).find_by(id:)
    else
      content_type_class.find_by(id:)
    end
    raise 'Website Section Content not found' unless content.present?
    content
  end

  def fetch_website_btn_style(id)
    button_style = if context[:current_user].type == 'Affiliate'
      context[:current_user].website_buttons.find_by(id:)
    else
      ::Websites::Style.find(id)
    end
    raise 'Website Button Style not found' unless button_style.present?
    button_style
  end

  def fetch_price_section_card(id)
    price_card = if context[:current_user].type == 'Affiliate'
      MultiTenantSupport.without_current_tenant do
        ::Websites::BodySectionPriceCard.joins(body_section_price: { body_section: :website }).where(websites: { user_id: context[:current_user].id }).find_by(id: id)
      end
    else
      Websites::BodySectionPriceCard.find_by(id:)
    end
    raise 'Price Card not found' unless price_card.present?
    price_card
  end

  def fetch_price_section_card_item(id)
    price_card_item = if context[:current_user].type == 'Affiliate'
      MultiTenantSupport.without_current_tenant do
        ::Websites::BodySectionPriceCardItem.joins(body_section_price_card: { body_section_price: { body_section: :website } }).where(websites: { user_id: context[:current_user].id }).find_by(id: id)
      end
    else
      Websites::BodySectionPriceCardItem.find_by(id:)
    end
    raise 'Price Card Item not found' unless price_card_item.present?
    price_card_item
  end

  def fetch_website_footer_column(id)
    footer_column = context[:current_tenant].website_footer_columns.find_by(id:)
    raise 'Website Footer Column not found' unless footer_column.present?
    footer_column
  end
end
