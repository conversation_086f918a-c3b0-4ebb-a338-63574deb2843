name: testing-flow
on:
  push:
    branches: [develop, staging, main]
  pull_request:
    branches: [develop, staging, main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        # needed because the postgres container does not provide a healthcheck
        # tmpfs makes DB faster by using RAM
        options: >-
          --mount type=tmpfs,destination=/var/lib/postgresql/data
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@master

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - name: Create db
        run: bin/rails db:prepare
        env:
          PGHOST: localhost
          PGUSER: postgres
          PGPASSWORD: postgres
          RAILS_ENV: test

      - name: Run tests
        env:
          PGHOST: localhost
          PGUSER: postgres
          PGPASSWORD: postgres
          RAILS_ENV: test
        run: bundle exec rspec spec