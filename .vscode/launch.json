{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Start Rails server",
            "type": "Ruby",
            "request": "launch",
            "cwd": "${workspaceRoot}",
            "program": "${workspaceRoot}/bin/rails",
            "args": [
                "server",
                "-p",
                "3000"
            ]
        },
        {
            "name": "Debug Rails server",
            "type": "rdbg",
            "request": "launch",
            "command": "rails",
            "script": "server",
            "args": [
                "-p",
                "3000"
            ]
        },
        {
            "name": "Run RSpec - all",
            "type": "Ruby",
            "request": "launch",
            "cwd": "${workspaceRoot}",
            "program": "${workspaceRoot}/bin/rspec",
            "args": [
                "spec"
            ]
        },
        {
            "name": "Debug RSpec - open spec file",
            "type": "Ruby",
            "request": "launch",
            "cwd": "${workspaceRoot}",
            "debuggerPort": "1235",
            "program": "${workspaceRoot}/bin/rspec",
            "args": [
                "${file}"
            ]
        },
        {
          "name": "Debug RSpec - open spec file on a certain line",
          "type": "Ruby",
          "request": "launch",
          "cwd": "${workspaceRoot}",
          "debuggerPort": "1235",
          "program": "${workspaceRoot}/bin/rspec",
          "args": ["${file}:${lineNumber}"]
        },
        // Credentials edit
        {
          "name": "Edit test credentials",
          "type": "Ruby",
          "request": "launch",
          "cwd": "${workspaceRoot}",
          "debuggerPort": "1235",
          "program": "${workspaceRoot}/bin/rails",
          "env": {
              "EDITOR": "code --wait"
          },
          "args": [
              "credentials:edit",
              "-e",
              "test"
          ]
        },
        {
          "name": "Edit development credentials",
          "type": "Ruby",
          "request": "launch",
          "cwd": "${workspaceRoot}",
          "debuggerPort": "1235",
          "program": "${workspaceRoot}/bin/rails",
          "env": {
              "EDITOR": "code --wait"
          },
          "args": [
              "credentials:edit",
              "-e",
              "development"
          ]
        },
        {
          "name": "Edit staging credentials",
          "type": "Ruby",
          "request": "launch",
          "cwd": "${workspaceRoot}",
          "debuggerPort": "1235",
          "program": "${workspaceRoot}/bin/rails",
          "env": {
              "EDITOR": "code --wait"
          },
          "args": [
              "credentials:edit",
              "-e",
              "staging"
          ]
        },
        {
          "name": "Edit production credentials",
          "type": "Ruby",
          "request": "launch",
          "cwd": "${workspaceRoot}",
          "debuggerPort": "1235",
          "program": "${workspaceRoot}/bin/rails",
          "env": {
              "EDITOR": "code --wait"
          },
          "args": [
              "credentials:edit",
              "-e",
              "production"
          ]
        }
    ]
}