diff --git a/app/graphql/mutations/create_website_footer.rb b/app/graphql/mutations/create_website_footer.rb
new file mode 100644
index 00000000..4820abd2
--- /dev/null
+++ b/app/graphql/mutations/create_website_footer.rb
@@ -0,0 +1,31 @@
+module Mutations
+  class CreateWebsiteFooter < BaseMutation
+    description "Creates a website footer for homepage"
+
+    field :website_footer, Types::Objects::WebsiteFooterType, null: false
+
+    argument :website_id, Integer, required: true, description: "ID of the homepage website"
+    argument :footer_input, Types::Inputs::WebsiteFooterInputType, required: true
+
+    def resolve(website_id:, footer_input:)
+      website = context[:current_tenant].websites.find(website_id)
+      
+      unless website.homepage?
+        raise GraphQL::ExecutionError, "Footer can only be created for homepage websites"
+      end
+      
+      if website.footer.present?
+        raise GraphQL::ExecutionError, "Footer already exists for this website"
+      end
+
+      website_footer = website.build_footer(
+        tenant: context[:current_tenant],
+        **footer_input
+      )
+      
+      raise GraphQL::ExecutionError.new "Error creating website footer", extensions: website_footer.errors.to_hash unless website_footer.save
+
+      { website_footer: website_footer }
+    end
+  end
+end
diff --git a/app/graphql/mutations/create_website_footer_column.rb b/app/graphql/mutations/create_website_footer_column.rb
new file mode 100644
index 00000000..e1fcf8c7
--- /dev/null
+++ b/app/graphql/mutations/create_website_footer_column.rb
@@ -0,0 +1,23 @@
+module Mutations
+  class CreateWebsiteFooterColumn < BaseMutation
+    description "Creates a website footer column"
+
+    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: false
+
+    argument :website_footer_id, Integer, required: true, description: "ID of the website footer"
+    argument :column_input, Types::Inputs::WebsiteFooterColumnInputType, required: true
+
+    def resolve(website_footer_id:, column_input:)
+      website_footer = context[:current_tenant].website_footers.find(website_footer_id)
+      
+      footer_column = website_footer.footer_columns.build(
+        tenant: context[:current_tenant],
+        **column_input
+      )
+      
+      raise GraphQL::ExecutionError.new "Error creating footer column", extensions: footer_column.errors.to_hash unless footer_column.save
+
+      { footer_column: footer_column }
+    end
+  end
+end
diff --git a/app/graphql/mutations/delete_website_footer_column.rb b/app/graphql/mutations/delete_website_footer_column.rb
new file mode 100644
index 00000000..8c4e67e7
--- /dev/null
+++ b/app/graphql/mutations/delete_website_footer_column.rb
@@ -0,0 +1,20 @@
+module Mutations
+  class DeleteWebsiteFooterColumn < BaseMutation
+    description "Deletes a website footer column"
+
+    field :success, Boolean, null: false
+    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: true
+
+    argument :id, Integer, required: true, description: "ID of the footer column to delete"
+
+    def resolve(id:)
+      footer_column = context[:current_tenant].website_footer_columns.find(id)
+      
+      if footer_column.destroy
+        { success: true, footer_column: footer_column }
+      else
+        raise GraphQL::ExecutionError.new "Error deleting footer column", extensions: footer_column.errors.to_hash
+      end
+    end
+  end
+end
diff --git a/app/graphql/mutations/update_website_footer.rb b/app/graphql/mutations/update_website_footer.rb
new file mode 100644
index 00000000..a3cc8e01
--- /dev/null
+++ b/app/graphql/mutations/update_website_footer.rb
@@ -0,0 +1,24 @@
+module Mutations
+  class UpdateWebsiteFooter < BaseMutation
+    description "Updates a website footer"
+
+    field :website_footer, Types::Objects::WebsiteFooterType, null: false
+
+    argument :website_id, Integer, required: true, description: "ID of the homepage website"
+    argument :footer_input, Types::Inputs::WebsiteFooterInputType, required: true
+
+    def resolve(website_id:, footer_input:)
+      website = context[:current_tenant].websites.find(website_id)
+      
+      unless website.homepage?
+        raise GraphQL::ExecutionError, "Footer can only be updated for homepage websites"
+      end
+      
+      website_footer = website.footer || website.ensure_footer!
+      
+      raise GraphQL::ExecutionError.new "Error updating website footer", extensions: website_footer.errors.to_hash unless website_footer.update(**footer_input)
+
+      { website_footer: website_footer }
+    end
+  end
+end
diff --git a/app/graphql/mutations/update_website_footer_column.rb b/app/graphql/mutations/update_website_footer_column.rb
new file mode 100644
index 00000000..37867c23
--- /dev/null
+++ b/app/graphql/mutations/update_website_footer_column.rb
@@ -0,0 +1,18 @@
+module Mutations
+  class UpdateWebsiteFooterColumn < BaseMutation
+    description "Updates a website footer column"
+
+    field :footer_column, Types::Objects::WebsiteFooterColumnType, null: false
+
+    argument :id, Integer, required: true, description: "ID of the footer column"
+    argument :column_input, Types::Inputs::WebsiteFooterColumnInputType, required: true
+
+    def resolve(id:, column_input:)
+      footer_column = context[:current_tenant].website_footer_columns.find(id)
+      
+      raise GraphQL::ExecutionError.new "Error updating footer column", extensions: footer_column.errors.to_hash unless footer_column.update(**column_input)
+
+      { footer_column: footer_column }
+    end
+  end
+end
diff --git a/app/graphql/types/enums/website_footer_select_style_type.rb b/app/graphql/types/enums/website_footer_select_style_type.rb
new file mode 100644
index 00000000..bfd93c63
--- /dev/null
+++ b/app/graphql/types/enums/website_footer_select_style_type.rb
@@ -0,0 +1,10 @@
+module Types
+  class Enums::WebsiteFooterSelectStyleType < Types::BaseEnum
+    description "Footer select style options"
+
+    value "ICONS_IN_BUBBLES", value: "icons_in_bubbles", description: "Icons displayed in bubble style"
+    value "ICONS_IN_RECTANGLE", value: "icons_in_rectangle", description: "Icons displayed in rectangle style"
+    value "ONLY_ICONS", value: "only_icons", description: "Display only icons without text"
+    value "ONLY_TEXT", value: "only_text", description: "Display only text without icons"
+  end
+end
diff --git a/app/graphql/types/enums/website_footer_template_type.rb b/app/graphql/types/enums/website_footer_template_type.rb
new file mode 100644
index 00000000..dca33313
--- /dev/null
+++ b/app/graphql/types/enums/website_footer_template_type.rb
@@ -0,0 +1,10 @@
+module Types
+  class Enums::WebsiteFooterTemplateType < Types::BaseEnum
+    description "Footer template options"
+
+    value "TEMPLATE1", value: "template1", description: "Template 1"
+    value "TEMPLATE2", value: "template2", description: "Template 2"
+    value "TEMPLATE3", value: "template3", description: "Template 3"
+    value "TEMPLATE4", value: "template4", description: "Template 4"
+  end
+end
diff --git a/app/graphql/types/inputs/website_footer_column_input_type.rb b/app/graphql/types/inputs/website_footer_column_input_type.rb
new file mode 100644
index 00000000..e874497d
--- /dev/null
+++ b/app/graphql/types/inputs/website_footer_column_input_type.rb
@@ -0,0 +1,9 @@
+module Types
+  class Inputs::WebsiteFooterColumnInputType < Types::BaseInputObject
+    description "Input type for website footer column"
+
+    argument :title, String, required: false
+    argument :links, GraphQL::Types::JSON, required: false
+    argument :position, Integer, required: false
+  end
+end
diff --git a/app/graphql/types/inputs/website_footer_input_type.rb b/app/graphql/types/inputs/website_footer_input_type.rb
new file mode 100644
index 00000000..a0a6d530
--- /dev/null
+++ b/app/graphql/types/inputs/website_footer_input_type.rb
@@ -0,0 +1,25 @@
+module Types
+  class Inputs::WebsiteFooterInputType < Types::BaseInputObject
+    description "Input type for website footer"
+
+    # Footer template and styling
+    argument :template_name, Types::Enums::WebsiteFooterTemplateType, required: false, description: "Footer template name"
+    argument :select_style, Types::Enums::WebsiteFooterSelectStyleType, required: false, description: "Footer select style"
+    argument :bg_text_icon_colour, String, required: false, description: "Background text and icon color (hex format)"
+    argument :bg_colour, String, required: false
+    argument :bg_colour_visibility, Boolean, required: false
+    argument :bg_image, GraphQL::Types::JSON, required: false
+    argument :bg_image_visibility, Boolean, required: false
+
+    # Contact information
+    argument :contact_information_visibility, Boolean, required: false
+    argument :contact_information_title, String, required: false
+    argument :contact_information_address, String, required: false
+
+    # Social networks
+    argument :social_networks_visibility, Boolean, required: false
+    # Footer columns
+    argument :footer_columns, [Types::Inputs::WebsiteFooterColumnInputType], required: false
+    argument :columns_visibility, Boolean, required: false
+  end
+end
diff --git a/app/graphql/types/inputs/websites/website_input_type.rb b/app/graphql/types/inputs/websites/website_input_type.rb
index 30084840..d5436a0d 100644
--- a/app/graphql/types/inputs/websites/website_input_type.rb
+++ b/app/graphql/types/inputs/websites/website_input_type.rb
@@ -20,5 +20,8 @@ module Types::Inputs::Websites
     argument :header_logo_sticky, GraphQL::Types::JSON, required: false
 
     argument :header_links, GraphQL::Types::JSON, required: false
+
+    # Footer configuration (only for homepage websites)
+    argument :footer, Types::Inputs::WebsiteFooterInputType, required: false
   end
 end
diff --git a/app/graphql/types/objects/website_footer_column_type.rb b/app/graphql/types/objects/website_footer_column_type.rb
new file mode 100644
index 00000000..f9e0d586
--- /dev/null
+++ b/app/graphql/types/objects/website_footer_column_type.rb
@@ -0,0 +1,11 @@
+module Types
+  class Objects::WebsiteFooterColumnType < Types::BaseObject
+    field :id, Integer, null: false
+    field :website_footer_id, Integer, null: false
+    field :title, String, null: false
+    field :links, GraphQL::Types::JSON, null: false
+    field :position, Integer, null: false
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+  end
+end
diff --git a/app/graphql/types/objects/website_footer_type.rb b/app/graphql/types/objects/website_footer_type.rb
new file mode 100644
index 00000000..bbc0a541
--- /dev/null
+++ b/app/graphql/types/objects/website_footer_type.rb
@@ -0,0 +1,30 @@
+module Types
+  class Objects::WebsiteFooterType < Types::BaseObject
+    field :id, Integer, null: false
+    field :website_id, Integer, null: false
+
+    # Footer template and styling
+    field :template_name, Types::Enums::WebsiteFooterTemplateType, null: false
+    field :select_style, Types::Enums::WebsiteFooterSelectStyleType, null: false
+    field :bg_text_icon_colour, String, null: false
+    field :bg_colour, String, null: false
+    field :bg_colour_visibility, Boolean, null: false
+    field :bg_image, Types::Objects::AttachmentType, null: true
+    field :bg_image_visibility, Boolean, null: false
+
+    # Contact information
+    field :contact_information_visibility, Boolean, null: false
+    field :contact_information_title, String, null: true
+    field :contact_information_address, String, null: true
+
+    # Social networks
+    field :social_networks_visibility, Boolean, null: false
+
+    # Footer columns
+    field :footer_columns, [Types::Objects::WebsiteFooterColumnType], null: false
+    field :columns_visibility, Boolean, null: false
+
+    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
+    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
+  end
+end
diff --git a/app/graphql/types/objects/website_type.rb b/app/graphql/types/objects/website_type.rb
index b0a3e00e..010822dd 100644
--- a/app/graphql/types/objects/website_type.rb
+++ b/app/graphql/types/objects/website_type.rb
@@ -21,6 +21,7 @@ module Types::Objects
     field :header_logo, Types::Objects::AttachmentType, null: true, resolver_method: :resolve_header_logo
     field :header_logo_sticky, Types::Objects::AttachmentType, null: true, resolver_method: :resolve_header_logo_sticky
     field :body_sections, [Types::Objects::Websites::BodySectionType], null: false
+    field :website_footer, Types::Objects::WebsiteFooterType, null: true, description: "Footer configuration (only for homepage)"
 
     # Define resolver methods dynamically
     %i[
diff --git a/app/models/script.rb b/app/models/script.rb
new file mode 100644
index 00000000..c546e1a8
--- /dev/null
+++ b/app/models/script.rb
@@ -0,0 +1,4 @@
+Tenant.all.each do |tenant|
+  home_page_website = tenant.websites.homepage?
+  home_page_website.ensure_homepage_footer
+end
\ No newline at end of file
diff --git a/app/models/tenant.rb b/app/models/tenant.rb
index 9226190b..c30755f2 100644
--- a/app/models/tenant.rb
+++ b/app/models/tenant.rb
@@ -7,7 +7,7 @@ class Tenant < ApplicationRecord
   has_many :websites, dependent: :destroy, autosave: true
   has_many :website_buttons, dependent: :destroy, class_name: "Websites::Style"
   has_many :signatures, dependent: :destroy, class_name: "Emails::Signature"
-
+  has_many :website_footer_columns, dependent: :destroy, class_name: "WebsiteFooterColumn"
   # has_one :super_admin, dependent: :destroy
 
   has_many :users, dependent: :destroy
diff --git a/app/models/website.rb b/app/models/website.rb
index 9a3c4912..8ad3c7db 100644
--- a/app/models/website.rb
+++ b/app/models/website.rb
@@ -1,6 +1,7 @@
 class Website < ApplicationRecord
   belongs_to_tenant :tenant, optional: :true
   has_many :body_sections, -> { where(parent_banner_id: nil).order(position: :asc) }, class_name: "Websites::BodySection", dependent: :destroy
+  has_one :website_footer, class_name: 'WebsiteFooter', dependent: :destroy
 
   validates :main_color, :header_color, :header_text_color, :footer_color, format: { with: /\A#(?:\h{3}){1,2}\z/, message: "must be an RGB color starting with #"}
   validates :title, presence: true
@@ -17,6 +18,7 @@ class Website < ApplicationRecord
   validate :unique_url_per_tenant_or_user
 
   after_commit :update_tenant_metric
+  after_create :ensure_homepage_footer
 
   def restrict_website_limits
     
@@ -160,6 +162,29 @@ class Website < ApplicationRecord
     return false
   end
 
+  def homepage?
+    title.downcase == 'home' && url == '/'
+  end
+
+  def create_footer_with_defaults!
+    return website_footer if website_footer.present?
+    return unless homepage?
+
+    footer_data = {
+      template_name: "template1",
+      select_style: "icons_in_bubbles",
+      bg_text_icon_colour: "#FFFFFF",
+      bg_colour: "#A270FE",
+      bg_colour_visibility: true,
+      bg_image_visibility: false,
+      contact_information_visibility: false,
+      social_networks_visibility: true,
+      columns_visibility: true
+    }
+
+    create_website_footer!(footer_data)
+  end
+
   private
 
   def update_tenant_metric
@@ -169,4 +194,9 @@ class Website < ApplicationRecord
       tenant&.tenant_metric&.update(pages_created: tenant.pages_count_without_home)
     end
   end
+
+  def ensure_homepage_footer
+    return unless homepage?
+    create_footer_with_defaults!
+  end
 end
diff --git a/app/models/website_footer.rb b/app/models/website_footer.rb
new file mode 100644
index 00000000..ce440bfe
--- /dev/null
+++ b/app/models/website_footer.rb
@@ -0,0 +1,38 @@
+class WebsiteFooter < ApplicationRecord
+  belongs_to_tenant :tenant
+  belongs_to :website
+  has_many :footer_columns, class_name: 'WebsiteFooterColumn', dependent: :destroy
+
+  include ImageUploader::Attachment.new(:bg_image, store: :public_store)
+
+  # Template name enum
+  enum template_name: {
+    template1: "template1",
+    template2: "template2",
+    template3: "template3",
+    template4: "template4"
+  }, _prefix: true
+
+  # Select style enum
+  enum select_style: {
+    icons_in_bubbles: "icons_in_bubbles",
+    icons_in_rectangle: "icons_in_rectangle",
+    only_icons: "only_icons",
+    only_text: "only_text"
+  }, _prefix: true
+
+  validates :website_id, uniqueness: true
+  validate :website_must_be_homepage
+  validates :template_name, presence: true
+  validates :select_style, presence: true
+
+  private
+
+  def website_must_be_homepage
+    return unless website
+
+    unless website.homepage?
+      errors.add(:website, "Footer can only be associated with homepage")
+    end
+  end
+end
diff --git a/app/models/website_footer_column.rb b/app/models/website_footer_column.rb
new file mode 100644
index 00000000..2848b44b
--- /dev/null
+++ b/app/models/website_footer_column.rb
@@ -0,0 +1,14 @@
+class WebsiteFooterColumn < ApplicationRecord
+  belongs_to_tenant :tenant
+  belongs_to :website_footer
+  
+  validates :title, presence: true
+  validates :position, presence: true, numericality: { greater_than: 0 }
+  
+  acts_as_list scope: :website_footer, column: :position
+  
+  # Ensure links is always an array
+  def links
+    super || []
+  end
+end
diff --git a/db/migrate/20250616080125_create_website_footers.rb b/db/migrate/20250616080125_create_website_footers.rb
new file mode 100644
index 00000000..b1e066ca
--- /dev/null
+++ b/db/migrate/20250616080125_create_website_footers.rb
@@ -0,0 +1,30 @@
+class CreateWebsiteFooters < ActiveRecord::Migration[7.0]
+  def change
+    create_table :website_footers do |t|
+      t.references :tenant, null: false, foreign_key: true
+      t.references :website, null: false, foreign_key: true
+
+      # Footer template and styling
+      t.string :template_name, default: "template1"
+      t.string :select_style, default: "icons_in_bubbles"
+      t.string :bg_text_icon_colour, default: "#FFFFFF"
+      t.string :bg_colour, default: "#A270FE"
+      t.boolean :bg_colour_visibility, default: true
+      t.jsonb :bg_image_data
+      t.boolean :bg_image_visibility, default: false
+
+      # Contact information
+      t.boolean :contact_information_visibility, default: false
+      t.string :contact_information_title
+      t.string :contact_information_address
+
+      # Social networks
+      t.boolean :social_networks_visibility, default: true
+
+      # Footer columns visibility
+      t.boolean :columns_visibility, default: true
+
+      t.timestamps
+    end
+  end
+end
diff --git a/db/migrate/20250616080128_create_website_footer_columns.rb b/db/migrate/20250616080128_create_website_footer_columns.rb
new file mode 100644
index 00000000..a219f5a3
--- /dev/null
+++ b/db/migrate/20250616080128_create_website_footer_columns.rb
@@ -0,0 +1,16 @@
+class CreateWebsiteFooterColumns < ActiveRecord::Migration[7.0]
+  def change
+    create_table :website_footer_columns do |t|
+      t.references :tenant, null: false, foreign_key: true
+      t.references :website_footer, null: false, foreign_key: true
+      
+      t.string :title, null: false
+      t.jsonb :links, default: [], null: false
+      t.integer :position, default: 1, null: false      
+      t.timestamps
+    end
+    
+    add_index :website_footer_columns, :position
+    add_index :website_footer_columns, :links, using: :gin
+  end
+end
