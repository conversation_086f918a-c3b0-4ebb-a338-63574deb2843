diff --git a/app/graphql/mutations/courses/create_lesson_presentation.rb b/app/graphql/mutations/courses/create_lesson_presentation.rb
new file mode 100644
index 00000000..83fff0ee
--- /dev/null
+++ b/app/graphql/mutations/courses/create_lesson_presentation.rb
@@ -0,0 +1,14 @@
+module Mutations
+  class Courses::CreateLessonPresentation < BaseMutation
+    field :lesson, Types::Objects::Courses::LessonPresentationType, null: false
+
+    argument :lesson, Types::Inputs::Courses::LessonPresentationInputType, required: true
+
+    def resolve(lesson:)
+      value = ::Courses::LessonPresentation.create_with_container!(**lesson)
+      {
+        lesson: value
+      }
+    end
+  end
+end
diff --git a/app/graphql/mutations/courses/update_lesson_presentation.rb b/app/graphql/mutations/courses/update_lesson_presentation.rb
new file mode 100644
index 00000000..e7878a36
--- /dev/null
+++ b/app/graphql/mutations/courses/update_lesson_presentation.rb
@@ -0,0 +1,15 @@
+module Mutations
+  module Courses
+    class UpdateLessonPresentation < ::Mutations::BaseMutation
+      field :lesson, Types::Objects::Courses::LessonPresentationType, null: false
+      argument :lesson_input, Types::Inputs::Courses::LessonPresentationInputType, required: true
+      argument :id, Integer, "The ID of the lesson to be updated", required: true
+    
+      def resolve(id:, lesson_input:)
+        lesson = ::Courses::LessonPresentation.find(id)
+        lesson.update!(**lesson_input)
+        { lesson: lesson }
+      end
+    end
+  end
+end
diff --git a/app/graphql/types/enums/courses/lesson_content_type_type.rb b/app/graphql/types/enums/courses/lesson_content_type_type.rb
index bc898ff1..1d25ba01 100644
--- a/app/graphql/types/enums/courses/lesson_content_type_type.rb
+++ b/app/graphql/types/enums/courses/lesson_content_type_type.rb
@@ -9,6 +9,7 @@ module Types
     value "EXAM", value: "Courses::LessonExam"
     value "HTML", value: "Courses::LessonHtml"
     value "PDF", value: "Courses::LessonPdf"
+    value "PRESENTATION", value: "Courses::LessonPresentation"
     value "SCORM", value: "Courses::LessonScorm"
     value "VIDEO", value: "Courses::LessonVideo"
     value "VIDEOCONF", value: "Courses::LessonVideocall"
diff --git a/app/graphql/types/inputs/courses/lesson_presentation_input_type.rb b/app/graphql/types/inputs/courses/lesson_presentation_input_type.rb
new file mode 100644
index 00000000..f0f6c88b
--- /dev/null
+++ b/app/graphql/types/inputs/courses/lesson_presentation_input_type.rb
@@ -0,0 +1,5 @@
+module Types
+  class Inputs::Courses::LessonPresentationInputType < Types::Inputs::Courses::LessonInputType
+    argument :content_id, Integer, "The ID of the associated LessonContent record", required: false
+  end
+end
diff --git a/app/graphql/types/interfaces/courses/lesson_content_type.rb b/app/graphql/types/interfaces/courses/lesson_content_type.rb
index 029ba214..9fb16189 100644
--- a/app/graphql/types/interfaces/courses/lesson_content_type.rb
+++ b/app/graphql/types/interfaces/courses/lesson_content_type.rb
@@ -17,6 +17,7 @@ module Types
       Types::Objects::Courses::LessonLegacyHtmlType,
       Types::Objects::Courses::LessonHtmlType,
       Types::Objects::Courses::LessonSurveyType,
+      Types::Objects::Courses::LessonPresentationType,
 
     )
 
@@ -45,6 +46,8 @@ module Types
         Types::Objects::Courses::LessonHtmlType
       when Courses::LessonSurvey
         Types::Objects::Courses::LessonSurveyType
+      when Courses::LessonPresentation
+        Types::Objects::Courses::LessonPresentationType
       else
         raise GraphQL::ExecutionError, "#{object.class} is not a lesson type"
       end
diff --git a/app/graphql/types/mutation_type.rb b/app/graphql/types/mutation_type.rb
index f075d919..21f5242b 100644
--- a/app/graphql/types/mutation_type.rb
+++ b/app/graphql/types/mutation_type.rb
@@ -163,6 +163,12 @@ module Types
     field :create_lesson_pdf, mutation: Mutations::Courses::CreateLessonPdf
     field :update_lesson_pdf, mutation: Mutations::Courses::UpdateLessonPdf
 
+    ###########################################
+    # Lesson - mutations - presentation       #
+    ###########################################
+    field :create_lesson_presentation, mutation: Mutations::Courses::CreateLessonPresentation
+    field :update_lesson_presentation, mutation: Mutations::Courses::UpdateLessonPresentation
+
     ###########################################
     # Lesson - mutations - downloadable        #
     ###########################################
diff --git a/app/graphql/types/objects/courses/lesson_presentation_type.rb b/app/graphql/types/objects/courses/lesson_presentation_type.rb
new file mode 100644
index 00000000..d0113021
--- /dev/null
+++ b/app/graphql/types/objects/courses/lesson_presentation_type.rb
@@ -0,0 +1,6 @@
+module Types
+  class Objects::Courses::LessonPresentationType < Types::Objects::Courses::LessonType
+    field :content_id, Integer, null: true
+    field :presentation, Types::Objects::AttachmentType, null: true
+  end
+end
diff --git a/app/models/courses/lesson.rb b/app/models/courses/lesson.rb
index 32276a99..7373b115 100644
--- a/app/models/courses/lesson.rb
+++ b/app/models/courses/lesson.rb
@@ -13,10 +13,11 @@ class Courses::Lesson < ApplicationRecord
   has_many :lesson_exams, class_name: 'Courses::LessonExam', foreign_key: :id
   has_many :lesson_surveys, class_name: 'Courses::LessonSurvey', foreign_key: :id
   has_one :video_content, class_name: 'Courses::LessonVideo', foreign_key: :id
+  has_one :presentation_content, class_name: 'Courses::LessonPresentation', foreign_key: :id
 
   counter_culture [:my_module, :course]
 
-  CONTENT_TYPES = %w[LessonVideo LessonExam LessonPdf LessonWebinar LessonDownloadable LessonAudio LessonVideocall LessonScorm LessonSurvey]
+  CONTENT_TYPES = %w[LessonVideo LessonExam LessonPdf LessonWebinar LessonDownloadable LessonAudio LessonVideocall LessonScorm LessonSurvey LessonPresentation]
 
   before_destroy :destroy_content!
 
diff --git a/app/models/courses/lesson_presentation.rb b/app/models/courses/lesson_presentation.rb
new file mode 100644
index 00000000..5fdcf488
--- /dev/null
+++ b/app/models/courses/lesson_presentation.rb
@@ -0,0 +1,27 @@
+class Courses::LessonPresentation < ApplicationRecord
+  include Courses::LessonContent
+
+  belongs_to :content, class_name: "Media::LessonContent", optional: true
+
+  validates :content_id, presence: true
+  validate :valid_presentation_file?
+
+  def presentation(*args)
+    content&.file(*args)
+  end
+
+  def valid_presentation_file?
+    return if content.blank? || content.file_data.blank?
+
+    filename = content.file_data.dig('metadata', 'filename')
+    if filename.blank?
+      errors.add(:content, "must have a filename")
+      return
+    end
+
+    extension = File.extname(filename).downcase
+    unless %w[.pptx .ppt .odp].include?(extension)
+      errors.add(:content, "must be a valid presentation file (.ppt, .pptx, .odp)")
+    end
+  end
+end
diff --git a/app/models/media/lesson_content.rb b/app/models/media/lesson_content.rb
index 5066017f..d542924c 100644
--- a/app/models/media/lesson_content.rb
+++ b/app/models/media/lesson_content.rb
@@ -9,6 +9,7 @@ class Media::LessonContent < ApplicationRecord
     pdf: "pdf",
     video: "video",
     audio: "audio",
+    presentation: "presentation",
     other: "other"
   }, _prefix: true
 
@@ -33,6 +34,10 @@ class Media::LessonContent < ApplicationRecord
       "audio"
     when /\Avideo\//
       "video"
+    when "application/vnd.ms-powerpoint",
+         "application/vnd.openxmlformats-officedocument.presentationml.presentation",
+         "application/vnd.oasis.opendocument.presentation"
+      "presentation"
     else
       "other"
     end
diff --git a/db/migrate/20250616080124_create_lesson_presentations.rb b/db/migrate/20250616080124_create_lesson_presentations.rb
new file mode 100644
index 00000000..f626d696
--- /dev/null
+++ b/db/migrate/20250616080124_create_lesson_presentations.rb
@@ -0,0 +1,14 @@
+class CreateLessonPresentations < ActiveRecord::Migration[7.0]
+  def change
+    create_table :courses_lesson_presentations, id: false do |t|
+      t.bigint :id, primary_key: true
+      t.references :tenant, null: false, foreign_key: true
+      t.references :content, null: true, foreign_key: { to_table: :media_lesson_contents }
+      t.jsonb :migration_data
+      t.jsonb :presentation_file_data
+      t.timestamps
+    end
+
+    add_foreign_key :courses_lesson_presentations, :courses_lessons, column: :id, on_update: :cascade, on_delete: :cascade
+  end
+end
diff --git a/db/migrate/20250618074249_add_lesson_presentation_count_in_media_lesson_content.rb b/db/migrate/20250618074249_add_lesson_presentation_count_in_media_lesson_content.rb
new file mode 100644
index 00000000..d1500bbc
--- /dev/null
+++ b/db/migrate/20250618074249_add_lesson_presentation_count_in_media_lesson_content.rb
@@ -0,0 +1,6 @@
+class AddLessonPresentationCountInMediaLessonContent < ActiveRecord::Migration[7.0]
+  def change
+    add_column :media_lesson_contents, :lesson_presentations_count, :integer, null: false, default: 0
+
+  end
+end
