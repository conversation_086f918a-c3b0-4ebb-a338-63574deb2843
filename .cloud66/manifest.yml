staging:
  rails:
    configuration:
      asset_pipeline_enabled: false
      asset_pipeline_precompile: false
      ruby_version: 3.2.1
      operating_system: ubuntu2204
production:
  rails:
    configuration:
      ruby_version: 3.2.1
      operating_system: ubuntu2204
      asset_pipeline_enabled: false
      asset_pipeline_precompile: false
      do_initial_db_schema_load: true
      activeprotect:
        http_ban_rate: 500
    servers:
      - server:
          unique_name: octopus
          vendor: googlecloud
          region: us-west1-a
          key_name: sabionet-production
          size: n2-standard-2
          root_disk_size: 25
      - server:
          unique_name: crow
          vendor: googlecloud
          region: us-west1-a
          key_name: sabionet-production
          size: e2-medium
          root_disk_size: 25
  postgresql:
    servers:
      - server:
          unique_name: kronosaurus
          vendor: googlecloud
          region: us-west1-a
          key_name: sabionet-production
          size: n2-highmem-2
          root_disk_size: 70
  redis:
    servers:
      - server:
          same_as: crow
        
  