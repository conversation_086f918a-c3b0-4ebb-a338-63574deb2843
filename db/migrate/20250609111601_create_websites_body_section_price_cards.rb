class CreateWebsitesBodySectionPriceCards < ActiveRecord::Migration[7.0]
  def change
    create_table :websites_body_section_price_cards do |t|
      t.references :tenant, null: true, foreign_key: true
      t.references :body_section_price, foreign_key: { to_table: :websites_body_section_prices }, index: { name: 'index_wbs_price_c_on_wbs_price_id' }
      
      # Card content
      t.jsonb :image_data
      t.string :background_color, default: '#FFFFFF'
      t.string :text_color, default: '#000000'
      t.string :font, default: "Poppins"

      t.string :title
      t.string :title_size, default: "14px"

      t.boolean :title_font_bold, default: false
      t.boolean :title_font_cursive, default: false
      t.boolean :title_font_underlined, default: false
      t.boolean :title_font_line_through, default: false

      t.string :title_font_alignment, default: 'center'

      t.string :subtitle
      t.string :subtitle_size, default: "12px"

      t.boolean :subtitle_font_bold, default: false
      t.boolean :subtitle_font_cursive, default: false
      t.boolean :subtitle_font_underlined, default: false
      t.boolean :subtitle_font_line_through, default: false

      t.string :subtitle_font_alignment, default: 'center'

      t.string :description

      t.boolean :description_font_bold, default: false
      t.boolean :description_font_cursive, default: false
      t.boolean :description_font_underlined, default: false
      t.boolean :description_font_line_through, default: false

      t.string :description_size, default: "10px"
      t.string :description_font_alignment, default: 'center'

      t.string :icon_color, default:"#2d3954"
      t.boolean :visibility, default: true

      # CTA Button
      t.string :button_type

      t.string :button_text, default: 'Get Started'
      t.string :button_url
      t.boolean :button_visibility, default: true

      t.integer :position, default: 1

      t.timestamps
    end

    add_index :websites_body_section_price_cards, :position
  end
end
