class CreateWebsiteFooters < ActiveRecord::Migration[7.0]
  def up
    create_table :website_footers do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :website, null: false, foreign_key: true

      # Footer template and styling
      t.integer :footer_template_number, default: 1
      t.string :bg_text_icon_colour, default: "#FFFFFF"
      t.string :bg_colour, default: "#A270FE"
      t.boolean :bg_colour_visibility, default: true
      t.jsonb :bg_image_data
      t.boolean :bg_image_visibility, default: false

      # Contact information
      t.boolean :contact_information_visibility, default: false
      t.string :contact_information_title
      t.string :contact_information_address

      # Social networks
      t.boolean :social_networks_visibility, default: true

      # Footer columns visibility
      t.boolean :columns_visibility, default: true

      t.timestamps
    end

    add_index :website_footers, :website_id, unique: true unless index_exists?(:website_footers, :website_id)
  end

  def down
    drop_table :website_footers if table_exists?(:website_footers)
  end
end
