class CreateWebsiteFooters < ActiveRecord::Migration[7.0]
  def up
    create_table :website_footers do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :website, null: false, foreign_key: true

      # Basic footer fields
      t.string :copyright_label, default: ""
      t.string :footer_color, default: "#A270FE"
      t.text :description

      # Social links (moved from websites table)
      t.jsonb :social_links, default: [], null: false

      # Contact information
      t.jsonb :contact_info, default: {}, null: false

      # Additional footer links/sections
      t.jsonb :additional_links, default: {}, null: false

      # Footer layout and styling options
      t.string :layout_style, default: "default" # default, minimal, extended
      t.boolean :show_social_links, default: true
      t.boolean :show_contact_info, default: false
      t.boolean :show_additional_links, default: false

      # Newsletter signup
      t.boolean :newsletter_enabled, default: false
      t.string :newsletter_title
      t.string :newsletter_description
      t.string :newsletter_button_text, default: "Subscribe"

      # Footer branding
      t.boolean :show_powered_by, default: true
      t.string :custom_footer_text

      t.timestamps
    end

    add_index :website_footers, :website_id, unique: true unless index_exists?(:website_footers, :website_id)
    add_index :website_footers, :social_links, using: :gin unless index_exists?(:website_footers, :social_links)
    add_index :website_footers, :contact_info, using: :gin unless index_exists?(:website_footers, :contact_info)
    add_index :website_footers, :additional_links, using: :gin unless index_exists?(:website_footers, :additional_links)
  end

  def down
    drop_table :website_footers if table_exists?(:website_footers)
  end
end
