class CreateWebsiteFooters < ActiveRecord::Migration[7.0]
  def change
    create_table :website_footers do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :website, null: false, foreign_key: true

      # Footer template and styling
      t.string :template_name, default: "template1"
      t.string :bg_text_icon_colour, default: "#FFFFFF"
      t.string :bg_colour, default: "#A270FE"
      t.boolean :bg_colour_visibility, default: true
      t.jsonb :bg_image_data
      t.boolean :bg_image_visibility, default: false

      # Contact information
      t.boolean :contact_information_visibility, default: false
      t.string :contact_information_title
      t.string :contact_information_address

      # Social networks
      t.boolean :social_networks_visibility, default: true

      # Footer columns visibility
      t.boolean :columns_visibility, default: true

      t.timestamps
    end
  end
end
