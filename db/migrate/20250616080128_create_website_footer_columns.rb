class CreateWebsiteFooterColumns < ActiveRecord::Migration[7.0]
  def change
    create_table :website_footer_columns do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :website_footer, null: false, foreign_key: true
      
      t.string :title, null: false
      t.jsonb :links, default: [], null: false
      t.integer :position, default: 1, null: false      
      t.timestamps
    end
    
    add_index :website_footer_columns, :position
    add_index :website_footer_columns, :links, using: :gin
  end
end
