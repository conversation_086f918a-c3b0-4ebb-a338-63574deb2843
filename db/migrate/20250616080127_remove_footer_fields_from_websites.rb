class RemoveFooterFieldsFromWebsites < ActiveRecord::Migration[7.0]
  def up
    # Skip removing fields - keep existing flow intact
    # The WebsiteFooter model will work alongside existing fields
    puts "Keeping existing footer fields in websites table for backward compatibility"
  end

  def down
    # Nothing to rollback since we didn't remove anything
    puts "No fields were removed, nothing to rollback"
  end
end
