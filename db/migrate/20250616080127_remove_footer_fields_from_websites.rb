class RemoveFooterFieldsFromWebsites < ActiveRecord::Migration[7.0]
  def up
    # Remove footer-specific fields from websites table
    remove_column :websites, :footer_copyright_label, :string
    remove_column :websites, :footer_color, :string
    remove_column :websites, :social_links, :jsonb
  end
  
  def down
    # Add the fields back if we need to rollback
    add_column :websites, :footer_copyright_label, :string, default: "", null: false
    add_column :websites, :footer_color, :string, default: "#A270FE", null: false
    add_column :websites, :social_links, :jsonb, default: [], null: false
    
    # Restore data from website_footers
    WebsiteFooter.includes(:website).find_each do |footer|
      website = footer.website
      next unless website
      
      website.update_columns(
        footer_copyright_label: footer.copyright_label,
        footer_color: footer.footer_color,
        social_links: footer.social_links
      )
    end
  end
end
