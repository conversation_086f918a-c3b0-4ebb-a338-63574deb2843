class CreateSurveyRelatedTables < ActiveRecord::Migration[7.0]
  def change
    # Lesson Surveys
    create_table :courses_lesson_surveys, id: false do |t|
      t.bigint :id, null: false, primary_key: true
      t.references :tenant, null: false, foreign_key: true
      t.string  :submit_button_text, default: "Submit"
      t.boolean :allow_anonymous, default: false
      t.text    :thank_you_message
      t.boolean :skippable, default: false
      t.timestamps
    end
    add_foreign_key :courses_lesson_surveys, :courses_lessons, column: :id, on_update: :cascade, on_delete: :cascade

    # Survey Questions
    create_table :courses_survey_questions do |t|
      t.references :tenant, null: false, foreign_key: true, index: { name: "index_courses_survey_questions_on_tenant_id" }
      t.references :survey, null: false, foreign_key: { to_table: :courses_lesson_surveys }, index: { name: "index_courses_survey_questions_on_exam_id" }
      t.string :type, null: false
      t.text :body, null: false
      t.integer :position, null: false
      t.references :related_lesson, foreign_key: { to_table: :courses_lessons }, index: { name: "index_courses_survey_questions_on_related_lesson_id" }
      t.jsonb :answer_options, default: [], null: false
      t.jsonb :migration_data
      t.timestamps
    end

    # Survey Responses
    create_table :subscriptions_survey_responses do |t|
      t.string :status, default: "initiated", null: false
      t.integer :attempt_number, default: 0, null: false
      t.references :subscription, foreign_key: { to_table: :subscriptions_subscriptions }, index: { name: "index_subscriptions_survey_responses_on_subscription_id" }
      t.references :survey, null: false, foreign_key: { to_table: :courses_lesson_surveys }, index: { name: "index_subscriptions_survey_responses_on_survey_id" }
      t.references :tenant, null: false, foreign_key: true, index: { name: "index_subscriptions_survey_responses_on_tenant_id" }
      t.datetime :finished_at
      t.timestamps
    end

    # Survey Questions per Response
    create_table :subscriptions_survey_questions do |t|
      t.references :tenant, null: false, foreign_key: true, index: { name: "index_subscriptions_survey_questions_on_tenant_id" }
      t.references :survey_response, null: false, foreign_key: { to_table: :subscriptions_survey_responses }, index: { name: "index_subscriptions_survey_questions_on_survey_response_id" }
      t.references :question, null: false, foreign_key: { to_table: :courses_survey_questions }, index: { name: "index_subscriptions_survey_questions_on_question_id" }
      t.timestamps
    end

    # Survey Answers
    create_table :subscriptions_survey_answers do |t|
      t.references :tenant, null: false, foreign_key: true, index: { name: "index_subscriptions_survey_answers_on_tenant_id" }
      t.string :type, null: false
      t.references :survey_response, null: false, foreign_key: { to_table: :subscriptions_survey_responses }, index: { name: "index_subscriptions_survey_answers_on_survey_response_id" }
      t.references :question, null: false, foreign_key: { to_table: :courses_survey_questions }, index: { name: "index_subscriptions_survey_answers_on_question_id" }
      t.references :user, foreign_key: true, index: { name: "index_subscriptions_survey_answers_on_user_id" }
      t.jsonb :answer_data
      t.jsonb :migration_data
      t.integer :time_taken, default: 0, comment: "Time taken to answer the question in seconds."
      t.timestamps null: false

      t.index [:tenant_id, :question_id, :survey_response_id], unique: true, name: "index_survey_answers_on_tenant_and_question_and_response"
    end
  end
end
