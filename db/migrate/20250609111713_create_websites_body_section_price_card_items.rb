class CreateWebsitesBodySectionPriceCardItems < ActiveRecord::Migration[7.0]
  def change
    create_table :websites_body_section_price_card_items do |t|
      t.references :tenant, null: true, foreign_key: true
      t.references :body_section_price_card, foreign_key: { to_table: :websites_body_section_price_cards }, index: { name: 'index_wbs_price_f_on_wbs_price_c_id' }
      t.string :text, null: false
      t.integer :position, default: 1
      
      t.timestamps
    end

    add_index :websites_body_section_price_card_items, :position
  end
end
