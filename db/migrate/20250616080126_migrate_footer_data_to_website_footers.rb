class MigrateFooterDataToWebsiteFooters < ActiveRecord::Migration[7.0]
  def up
    # Find all homepage websites and create footer records for them
    homepage_websites = Website.where(title: 'home', url: '/')
    
    homepage_websites.find_each do |website|
      next if WebsiteFooter.exists?(website: website)
      
      # Create footer with existing data
      WebsiteFooter.create!(
        tenant: website.tenant,
        website: website,
        copyright_label: website.read_attribute(:footer_copyright_label) || "",
        footer_color: website.read_attribute(:footer_color) || "#A270FE",
        social_links: website.read_attribute(:social_links) || []
      )
    end
  end
  
  def down
    # Copy footer data back to websites table
    WebsiteFooter.includes(:website).find_each do |footer|
      website = footer.website
      next unless website
      
      website.update_columns(
        footer_copyright_label: footer.copyright_label,
        footer_color: footer.footer_color,
        social_links: footer.social_links
      )
    end
    
    # Remove all footer records
    WebsiteFooter.delete_all
  end
end
